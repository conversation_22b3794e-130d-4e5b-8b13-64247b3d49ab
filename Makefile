.SILENT: clean install-cdk synth test deploy
.PHONY: clean install-cdk synth test deploy

help: ## Show help
	@fgrep -h "##" $(MAKEFILE_LIST) | fgrep -v fgrep | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

clean: ## Clean directories
	rm -rfv .aws-sam/ && rm -rfv cdk.out/ && rm -rfv asset.* && rm -rfv __pycache__/
	find src -depth -name "__pycache__" | xargs rm -rfv

install-cdk: clean ## Install the requirements for deployment.
	@export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain bhub --domain-owner 100154399918 --region us-east-1 --query authorizationToken --output text --profile shared-infrastructure-prod`; \
		pip config set global.extra-index-url https://aws:$$<EMAIL>/pypi/bhub-cdk/simple/; \
		pip install -r requirements/cdk.txt

synth: clean ## Produces CDK synthesis
	@test -n "$(profile)" || (echo "profile is not set. Target usage: make synth profile=(bucaasdev|bucaasprod)"; exit 1)
	$(info * Synthetizing CDK application...)
	cdk synth \
	--parameters AppEnv='dev' \
	--profile $(profile)

database:
	docker compose up -d
	sleep 10;

new-migration: ## Call with `make migrate message="Migration message"`
	cd src; alembic revision --autogenerate -m "$(message)"

run-migrations:
	cd src; alembic upgrade head

run-migrations-test:
	cd src; APP_ENV=test alembic upgrade head

deploy: clean ## Deploy
	@test -n "$(profile)" || (echo "profile is not set. Target usage: make deploy profile=(bucaasdev|bucaasprod) env=(dev|prod)"; exit 1)
	@test -n "$(env)" || (echo "env is not set. Target usage: make deploy profile=(bucaasdev|bucaasprod) env=(staging|prod)"; exit 1)
	DOCKER_BUILDKIT=1 DOCKER_DEFAULT_PLATFORM='linux/amd64' cdk deploy ConciliadorBackStack \
	--parameters AppEnv='$(env)' \
	--parameters EncryptKey='$(shell git secret cat credentials/encrypt_key.txt.secret)' \
	--parameters ExternalQueueUrl='$(shell cat credentials/external_queue_url_$(env).txt)' \
	--parameters MatchBucket='$(shell cat credentials/match_bucket_$(env).txt)' \
	--parameters AccountBucket='$(shell cat credentials/account_bucket_$(env).txt)' \
	--parameters CamundaEndpoint='$(shell cat credentials/camunda_endpoint_$(env).txt)' \
	--parameters CamundaUsername='$(shell cat credentials/camunda_username_$(env).txt)' \
	--parameters CamundaPassword='$(shell git secret cat credentials/camunda_password_$(env).txt)' \
	--parameters CamundaProcessKey='$(shell cat credentials/camunda_process_key_$(env).txt)' \
	--parameters TransactionsApiBaseUrl='$(shell cat credentials/transactions_api_base_url_$(env).txt)' \
	--parameters CockpitApiBaseUrl='$(shell cat credentials/cockpit_api_base_url_$(env).txt)' \
	--parameters UploadApiBaseUrl='$(shell cat credentials/upload_api_base_url_$(env).txt)' \
	--parameters FeatureFlagSimplifiedCategorizer='$(shell grep FEATURE_FLAG_SIMPLIFIED_CATEGORIZER .env.dist | cut -d "=" -f2)' \
	--parameters BinderArn='$(shell cat credentials/binder_arn_$(env).txt)' \
	-c deploy_env='$(env)' \
	--profile $(profile)
