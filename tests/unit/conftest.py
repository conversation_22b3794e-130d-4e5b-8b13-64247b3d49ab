import os

import boto3
import pytest
from fastapi.testclient import TestClient
from moto import mock_dynamodb2, mock_s3, mock_sqs

from shared.auth import Claims, User, auth

os.environ["APP_ENV"] = "test"

from infrastructure.database import DatabaseBaseModel  # noqa
from shared.session_manager import session_context, session_scope  # noqa

os.environ["AWS_ACCESS_KEY_ID"] = "testing"
os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
os.environ["AWS_SECURITY_TOKEN"] = "testing"
os.environ["AWS_SESSION_TOKEN"] = "testing"
os.environ["AWS_DEFAULT_REGION"] = "us-east-1"
os.environ["AWS_REGION"] = "us-east-1"
os.environ["EXTRACT_BALANCE_QUEUE"] = "queue"

os.environ["ENCRYPT_KEY"] = "abcdefghij"
os.environ["PLUGGY_HOST"] = "http://test.pluggy"
os.environ["PLUGGY_CLIENT_ID"] = "client_id"
os.environ["PLUGGY_CLIENT_SECRET"] = "client_secret"

os.environ["CAMUNDA_ENDPOINT"] = "test"
os.environ["CAMUNDA_USERNAME"] = "test"
os.environ["CAMUNDA_PASSWORD"] = "test"
os.environ["CAMUNDA_PROCESS_KEY"] = "test"

os.environ["INVOICES_API_V1_BASEURL"] = "http://chupacabraapiv1.test"
os.environ["INVOICES_API_V1_APIKEY"] = "test-123-abc"
os.environ["MATCH_BUCKET"] = "test"
os.environ["COCKPIT_API_BASEURL"] = "http://test.cockpit"
os.environ["TRANSACTIONS_API_BASEURL"] = "http://test.transactions"

from main import app as fastapi_app  # noqa


@pytest.fixture
def aws_credentials():
    """Mocked AWS Credentials for moto."""
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "us-east-1"


@pytest.fixture
def s3(aws_credentials):
    with mock_s3():
        conn = boto3.resource("s3")
        yield conn


@pytest.fixture(scope="function")
def dynamodb_local():
    with mock_dynamodb2():
        yield boto3.client("dynamodb")


@pytest.fixture
def queue_name():
    return "my-test-queue"


@pytest.fixture
def sqs_client_mocked(aws_credentials):
    with mock_sqs():
        conn = boto3.client("sqs")
        yield conn


@pytest.fixture
def sqs_test(sqs_client_mocked, queue_name):
    sqs_client_mocked.create_queue(QueueName=queue_name)
    yield


def clear_data():
    session = session_context.get()
    try:
        meta = DatabaseBaseModel.metadata
        for table in reversed(meta.sorted_tables):
            session.execute(table.delete())
        session.commit()
    except Exception as e:
        session.rollback()
        print(f"Erro ao limpar dados: {e}")


@pytest.fixture
def db_test():
    clear_data()
    yield


@pytest.fixture
def app():
    from main import app

    yield app


@pytest.fixture
def client(app):
    with TestClient(app) as client:
        yield client


@pytest.fixture
def test_auth_client():
    with TestClient(app=fastapi_app) as client:
        fastapi_app.dependency_overrides[auth.verify] = lambda: Claims(
            sub="google-oauth2|123",
            owner=User(name="John Doe", email="<EMAIL>", token="test-token"),
            iss="https://example.com",
            iat=1630070400,
            exp=1630074000,
            aud="example",
        )
        yield client
