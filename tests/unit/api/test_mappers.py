from datetime import datetime

import pytest

from api.mappers import ReconciliationResponse
from domain.models import (
    JournalHeader,
    JournalLine,
    LinkedInvoice,
    PartnerEntity,
    Reconciliation,
    ReconciliationStatusEnum,
    SuggestedInvoice,
    SuggestionTypeEnum,
    TransactionStatusEnum,
)


@pytest.fixture
def mock_partner(db_test):
    """Ensures a partner entity is saved before being referenced."""
    partner = PartnerEntity(
        cpf_cnpj="12345678901",
        name="Test Supplier",
    )
    partner.save()
    return partner


@pytest.fixture
def mock_reconciliation(db_test):
    """Creates a base reconciliation instance."""
    reconciliation = Reconciliation(
        competence="2025-02",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="CUST-001",
        operator="User A",
    ).save()
    return reconciliation


@pytest.fixture
def mock_journal_header(mock_reconciliation):
    """Creates a base JournalHeader object."""
    journal_header = JournalHeader(
        amount=200.00,
        date="2025-02-25T12:00:00",
        description="Mock Journal Header",
        status="NEW",
        cockpit_account_id="ACC-TEST",
        reconciliation_id=mock_reconciliation.id,
        source_reference_id="txn-TEST",
        _metadata={"is_linked_invoice_applicable": None, "has_linked_invoices": None},
    ).save()

    return JournalHeader.get(journal_header.id)


@pytest.fixture
def mock_reconciliation_with_data(mock_partner, mock_reconciliation):
    """
    Creates a reconciliation with transactions and journal headers,
        ensuring data consistency.
    """
    journal_headers = [
        JournalHeader(
            amount=-150.00,
            date="2025-02-25T12:00:00",
            description="Expense Transaction",
            status=TransactionStatusEnum.RECONCILED,
            cockpit_account_id="ACC-001",
            reconciliation_id=mock_reconciliation.id,
            source_reference_id="txn-001",
            partner_entity_id=mock_partner.id,
            _metadata={
                "is_linked_invoice_applicable": None,
                "has_linked_invoices": None,
            },
        ).save(),
        JournalHeader(
            amount=150.00,
            date="2025-02-25T12:00:00",
            description="Income Transaction",
            status=TransactionStatusEnum.RECONCILED,
            cockpit_account_id="ACC-002",
            reconciliation_id=mock_reconciliation.id,
            source_reference_id="txn-002",
            partner_entity_id=mock_partner.id,
            _metadata={
                "is_linked_invoice_applicable": None,
                "has_linked_invoices": None,
            },
        ).save(),
    ]

    journal_lines = [
        JournalLine(
            journal_header=journal_headers[0],
            line_type="DEBIT",
            account_id="ACC-002",
            amount=150.00 * -1,
        ).save(),
        JournalLine(
            journal_header=journal_headers[0],
            line_type="CREDIT",
            account_id="ACC-001",
            amount=150.00,
        ).save(),
        JournalLine(
            journal_header=journal_headers[1],
            line_type="DEBIT",
            account_id="ACC-004",
            amount=150.00 * -1,
        ).save(),
        JournalLine(
            journal_header=journal_headers[1],
            line_type="CREDIT",
            account_id="ACC-003",
            amount=150.00,
        ).save(),
    ]

    SuggestedInvoice(
        platform_transaction_id="txn-001",
        platform_invoice_id="inv-001",
        ranking=1,
        exact_match=True,
        invoice_date="2025-02-26",
        invoice_company_name="Example Company",
        invoice_value="150",
        invoice_number="INV-123",
        suggestion_type=SuggestionTypeEnum.INVOICE,
    ).save()

    SuggestedInvoice(
        platform_transaction_id="txn-002",
        platform_invoice_id="inv-002",
        ranking=1,
        exact_match=True,
        invoice_date="2025-02-26",
        invoice_company_name="Example Company",
        invoice_value="300",
        invoice_number="INV-456",
        suggestion_type=SuggestionTypeEnum.INVOICE,
    ).save()

    LinkedInvoice(
        platform_invoice_id="inv-001",
        invoice_number="INV-123",
        journal_line_id=journal_lines[1].id,
        link_date=datetime.utcnow(),
        link_percentual=100.0,
    ).save()

    LinkedInvoice(
        platform_invoice_id="inv-002",
        invoice_number="INV-456",
        journal_line_id=journal_lines[3].id,
        link_date=datetime.utcnow(),
        link_percentual=100.0,
    ).save()

    return mock_reconciliation


def test_reconciliation_response_build_with_journal_model(
    mock_reconciliation_with_data, mock_partner
):
    response = ReconciliationResponse.build(mock_reconciliation_with_data.id)

    assert str(mock_reconciliation_with_data.id) == str(response.id)
    assert mock_reconciliation_with_data.competence == response.competence
    assert mock_reconciliation_with_data.status == response.status
    assert (
        mock_reconciliation_with_data.cockpit_customer_id
        == response.cockpit_customer_id
    )
    assert mock_reconciliation_with_data.operator == response.operator

    assert len(response.transactions) == 2

    expense_transaction = next(t for t in response.transactions if t.amount < 0)
    income_transaction = next(t for t in response.transactions if t.amount > 0)

    assert expense_transaction.amount == -150.00
    assert expense_transaction.description == "Expense Transaction"
    assert expense_transaction.status == "RECONCILED"
    assert expense_transaction.cockpit_account_id == "ACC-001"
    assert expense_transaction.platform_transaction_id == "txn-001"

    assert len(expense_transaction.journal_lines) == 2

    debit_line = next(
        line for line in expense_transaction.journal_lines if line.line_type == "DEBIT"
    )
    credit_line = next(
        line for line in expense_transaction.journal_lines if line.line_type == "CREDIT"
    )
    assert debit_line.expense_account == "ACC-002"
    assert debit_line.amount == 150.00
    assert credit_line.income_account == "ACC-001"
    assert credit_line.amount == 150.00

    assert income_transaction.amount == 150.00
    assert income_transaction.description == "Income Transaction"
    assert income_transaction.status == "RECONCILED"
    assert income_transaction.cockpit_account_id == "ACC-002"
    assert income_transaction.platform_transaction_id == "txn-002"

    assert len(income_transaction.journal_lines) == 2

    debit_line = next(
        line for line in income_transaction.journal_lines if line.line_type == "DEBIT"
    )
    credit_line = next(
        line for line in income_transaction.journal_lines if line.line_type == "CREDIT"
    )
    assert debit_line.expense_account == "ACC-004"
    assert debit_line.amount == 150.00
    assert credit_line.income_account == "ACC-003"
    assert credit_line.amount == 150.00

    assert expense_transaction.trading_entity.cpf_cnpj == mock_partner.cpf_cnpj
    assert expense_transaction.trading_entity.name == mock_partner.name
    assert income_transaction.trading_entity.cpf_cnpj == mock_partner.cpf_cnpj
    assert income_transaction.trading_entity.name == mock_partner.name

    assert "ACC-001" in response.bank_account_count
    assert "ACC-002" in response.bank_account_count
    assert response.bank_account_count["ACC-001"]["total"] == 1
    assert response.bank_account_count["ACC-001"]["reconciled"] == 1
    assert response.bank_account_count["ACC-002"]["total"] == 1
    assert response.bank_account_count["ACC-002"]["reconciled"] == 1
