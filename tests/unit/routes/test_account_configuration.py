import uuid
from datetime import datetime
from unittest.mock import patch

import pytest

from domain.models import (
    AccountConfigurationTypeEnum,
    JournalHeader,
    JournalLine,
    Reconciliation,
    ReconciliationStatusEnum,
    TransactionStatusEnum,
)


@pytest.fixture
def reconciliation(db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
        customer_cnpj="**************",
    )
    reconciliation.save()
    return reconciliation


@pytest.fixture
def journal_with_lines(reconciliation):
    journal_header = JournalHeader(
        amount=-200.00,
        cockpit_account_id="123",
        date=datetime.now(),
        description="Journal Entry with Supplier",
        notes="Test notes",
        status=TransactionStatusEnum.NEW,
        reconciliation_id=reconciliation.id,
    )
    journal_header.save()

    debit_line = JournalLine(
        line_type="DEBIT",
        amount=200.00,
        description="Debit Entry",
        journal_header=journal_header,
        account_id="123",
    )
    debit_line.save()

    credit_line = JournalLine(
        line_type="CREDIT",
        amount=200.00,
        description="Credit Entry",
        journal_header=journal_header,
        account_id=None,
    )
    credit_line.save()

    return journal_header, debit_line, credit_line


def test_post_account_configuration_success(test_auth_client, db_test, reconciliation):
    body = {
        "code": str(uuid.uuid4()),
        "cockpit_account_id": "cockpit_account_123",
        "reconciliation_id": str(reconciliation.id),
    }

    response = test_auth_client.post("/account-configuration/", json=body)

    assert response.status_code == 200


def test_post_account_exists_configuration_success(
    test_auth_client, db_test, reconciliation
):
    body = {
        "code": str(uuid.uuid4()),
        "cockpit_account_id": "cockpit_account_123",
        "reconciliation_id": str(reconciliation.id),
    }

    response = test_auth_client.post("/account-configuration/", json=body)

    assert response.status_code == 200

    body = {
        "code": str(uuid.uuid4()),
        "cockpit_account_id": "cockpit_account_123",
        "reconciliation_id": str(reconciliation.id),
    }

    response = test_auth_client.post("/account-configuration/", json=body)

    assert response.status_code == 200


def test_post_account_configuration_update_journals_success(
    test_auth_client, db_test, reconciliation, journal_with_lines
):
    _, debit_line, credit_line = journal_with_lines

    body = {
        "code": "BANK_CODE",
        "cockpit_account_id": "cockpit_account_123",
        "reconciliation_id": str(reconciliation.id),
    }

    response = test_auth_client.post("/account-configuration/", json=body)

    assert response.status_code == 200

    assert credit_line.account_id == "BANK_CODE"
    assert debit_line.account_id == "123"


def test_post_account_configuration_transaction_not_found(test_auth_client, db_test):

    body = {
        "code": "BANK_CODE",
        "cockpit_account_id": "cockpit_account_123",
        "reconciliation_id": "reconciliation_123",
    }

    response = test_auth_client.post("/account-configuration/", json=body)

    assert response.status_code == 404


@patch("application.account_configuration_service.set_account_configuration")
def test_post_account_configuration_general_exception(
    mock_set_account_configuration, test_auth_client
):
    mock_set_account_configuration.side_effect = Exception("Unexpected error")

    body = {
        "code": "BANK_CODE",
        "cockpit_account_id": "cockpit_account_123",
        "reconciliation_id": "reconciliation_123",
    }

    with pytest.raises(Exception):
        response = test_auth_client.post("/account-configuration/", json=body)

        assert response.status_code == 400
        assert response.json() == {"detail": "Unexpected error"}
        mock_set_account_configuration.assert_called_once_with(
            cockpit_account_id="cockpit_account_123",
            bank_code="BANK_CODE",
            account_configuration_type=AccountConfigurationTypeEnum.CHART_OF_ACCOUNTS_CODE,  # noqa
            reconciliation_id="reconciliation_123",
        )


def test_post_account_configuration_invalid_data(test_auth_client, db_test):
    body = {
        "code": "BANK_CODE",
        "cockpit_account_id": "cockpit_account_123",
    }

    response = test_auth_client.post("/account-configuration/", json=body)

    assert response.status_code == 422
    assert "detail" in response.json()


def test_post_account_configuration_extra_fields(client):
    body = {
        "code": "BANK_CODE",
        "cockpit_account_id": "cockpit_account_123",
        "reconciliation_id": "reconciliation_123",
        "extra_field": "some_value",
    }

    response = client.post("/account-configuration/", json=body)

    assert response.status_code == 422
    assert "detail" in response.json()


def test_post_account_configuration_missing_required_field(test_auth_client):
    body = {"code": "BANK_CODE", "reconciliation_id": "reconciliation_123"}

    response = test_auth_client.post("/account-configuration/", json=body)

    assert response.status_code == 422
    assert "detail" in response.json()
