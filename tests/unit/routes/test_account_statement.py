import io
from datetime import datetime
from unittest.mock import AsyncMock

import pytest
import requests
import responses
from fastapi import H<PERSON><PERSON><PERSON><PERSON><PERSON>, status
from freezegun import freeze_time

import api.account_statement_routes as account_statement_routes
from api.account_statement_routes import validate_file_extension
from domain.models import (
    BankAccountStatus,
    FileUploadStatus,
    Reconciliation,
    ReconciliationFileUpload,
    ReconciliationStatusEnum,
)
from infrastructure.upload_api_client import UploadApiClient
from main import app


@pytest.fixture
def mock_upload_api(monkeypatch):
    mock = AsyncMock(spec=UploadApiClient)
    app.dependency_overrides[account_statement_routes.get_tio_patinhas_api] = (
        lambda: mock
    )
    return mock


@pytest.fixture
def test_reconciliation(db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["account-123"],
        status_bank_account_guids={"account-123": BankAccountStatus.EXTRACT_IMPORTED},
    )
    reconciliation.save()
    return reconciliation


def test_validate_file_extension_invalid():
    with pytest.raises(HTTPException) as excinfo:
        validate_file_extension("statement.txt")
    assert excinfo.value.status_code == status.HTTP_400_BAD_REQUEST


@freeze_time("2025-01-01 12:00:00")
@pytest.mark.asyncio
async def test_upload_account_statement_success(
    client, db_test, test_reconciliation, test_auth_client, mock_upload_api
):
    mock_upload_api.upload_file.return_value = {
        "id": "upload-123",
        "status": "UPLOADED",
    }

    file_content = b"Fake PDF content"
    response = client.post(
        "/account-statement/upload",
        files={"file": ("statement.pdf", io.BytesIO(file_content), "application/pdf")},
        data={
            "bankaccount_guid": "account-123",
            "reconciliation_id": str(test_reconciliation.id),
        },
    )

    assert response.status_code == status.HTTP_201_CREATED
    response = response.json()
    assert response == {
        "latest_uploads_by_account": {
            "account-123": {
                "status": FileUploadStatus.UPLOADED,
                "upload_id": "upload-123",
                "error_message": None,
                "metadata": {
                    "upload_response": {"status": "UPLOADED", "id": "upload-123"},
                    "upload_id": "upload-123",
                    "status": "UPLOADED",
                    "uploaded_at": "2025-01-01T12:00:00",
                },
            }
        },
        "status_bank_account_guids": {"account-123": "EXTRACT_IMPORTING"},
    }

    upload = ReconciliationFileUpload.all()

    assert upload is not None
    assert len(upload) == 1
    assert upload[0].upload_id == "upload-123"
    assert upload[0].file_type == "application/pdf"
    assert upload[0].status == FileUploadStatus.UPLOADED

    reconciliation = Reconciliation.get(str(test_reconciliation.id))

    assert reconciliation.status_bank_account_guids == {
        "account-123": BankAccountStatus.EXTRACT_IMPORTING
    }


@pytest.mark.asyncio
async def test_upload_account_statement_invalid_extension(
    client, db_test, test_reconciliation
):
    file_content = b"Invalid content"
    response = client.post(
        "/account-statement/upload",
        files={"file": ("invalid.txt", io.BytesIO(file_content), "text/plain")},
        data={
            "bankaccount_guid": "account-123",
            "reconciliation_id": str(test_reconciliation.id),
        },
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.asyncio
async def test_upload_account_statement_invalid_account(
    client, db_test, test_reconciliation, mock_upload_api, test_auth_client
):
    file_content = b"Fake PDF content"
    response = client.post(
        "/account-statement/upload",
        files={"file": ("statement.pdf", io.BytesIO(file_content), "application/pdf")},
        data={
            "bankaccount_guid": "invalid-account",
            "reconciliation_id": str(test_reconciliation.id),
        },
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_upload_account_statement_reconciliation_not_found(
    client, db_test, mock_upload_api, test_auth_client
):
    file_content = b"Fake PDF content"
    response = client.post(
        "/account-statement/upload",
        files={"file": ("statement.pdf", io.BytesIO(file_content), "application/pdf")},
        data={
            "bankaccount_guid": "account-123",
            "reconciliation_id": "********-0000-0000-0000-********0000",
        },
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_upload_account_statement_generic_failure(
    client, db_test, test_reconciliation, mock_upload_api, test_auth_client
):
    mock_upload_api.upload_file.side_effect = Exception("API Error")

    file_content = b"Fake PDF content"
    response = client.post(
        "/account-statement/upload",
        files={"file": ("statement.pdf", io.BytesIO(file_content), "application/pdf")},
        data={
            "bankaccount_guid": "account-123",
            "reconciliation_id": str(test_reconciliation.id),
        },
    )

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    uploads = ReconciliationFileUpload.all()
    assert len(uploads) == 1
    assert uploads[0].status == FileUploadStatus.FAILED
    assert uploads[0].error_message == "API Error"


@pytest.mark.asyncio
async def test_upload_account_statement_without_file(
    client, db_test, test_reconciliation
):
    response = client.post(
        "/account-statement/upload",
        data={
            "bankaccount_guid": "account-123",
            "reconciliation_id": str(test_reconciliation.id),
        },
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
async def test_upload_account_statement_api_failure(
    client, db_test, test_reconciliation, mock_upload_api, test_auth_client
):
    mock_upload_api.upload_file.side_effect = requests.exceptions.HTTPError("API Error")

    file_content = b"Fake PDF content"
    response = client.post(
        "/account-statement/upload",
        files={"file": ("statement.pdf", io.BytesIO(file_content), "application/pdf")},
        data={
            "bankaccount_guid": "account-123",
            "reconciliation_id": str(test_reconciliation.id),
        },
    )

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    uploads = ReconciliationFileUpload.all()
    assert len(uploads) == 1
    assert uploads[0].status == FileUploadStatus.FAILED
    assert uploads[0].error_message == "API Error"


@pytest.mark.asyncio
async def test_upload_account_statement_file_not_found_failure(
    client, db_test, test_reconciliation, mock_upload_api, test_auth_client
):
    mock_upload_api.upload_file.side_effect = FileNotFoundError

    file_content = b"Fake PDF content"
    response = client.post(
        "/account-statement/upload",
        files={"file": ("statement.pdf", io.BytesIO(file_content), "application/pdf")},
        data={
            "bankaccount_guid": "account-123",
            "reconciliation_id": str(test_reconciliation.id),
        },
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST

    uploads = ReconciliationFileUpload.all()
    assert len(uploads) == 0


@freeze_time("2025-01-01 12:00:00")
@pytest.mark.asyncio
async def test_update_account_statement_success(
    client, db_test, test_reconciliation, test_auth_client, mock_upload_api
):
    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(test_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.UPLOADED,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": datetime.now().isoformat(),
    }
    ReconciliationFileUpload.create(**upload_record)

    mock_upload_api.validate_upload.return_value = {"invalid_data": []}

    response = client.post(
        "/account-statement/upload/upload-123/validate",
    )

    assert response.status_code == status.HTTP_200_OK
    response = response.json()
    assert response == {
        "latest_uploads_by_account": {
            "account-123": {
                "error_message": None,
                "status": FileUploadStatus.PROCESSING,
                "upload_id": "upload-123",
                "metadata": {"upload_response": {"id": "upload-123"}},
            }
        },
        "status_bank_account_guids": {"account-123": "EXTRACT_PROCESSING"},
    }

    upload: ReconciliationFileUpload = ReconciliationFileUpload.filter(
        ReconciliationFileUpload.upload_id == "upload-123"
    )[0]
    assert upload.status == FileUploadStatus.PROCESSING

    reconciliation = Reconciliation.get(str(test_reconciliation.id))
    assert (
        reconciliation.status_bank_account_guids["account-123"]
        == BankAccountStatus.EXTRACT_PROCESSING
    )


@freeze_time("2025-01-01 12:00:00")
@pytest.mark.asyncio
async def test_update_account_validate_error(
    client, db_test, test_reconciliation, test_auth_client, mock_upload_api
):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["account-123"],
        status_bank_account_guids={"account-123": BankAccountStatus.EXTRACT_IMPORTING},
    ).save()

    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.UPLOADED,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": datetime.now().isoformat(),
    }
    ReconciliationFileUpload.create(**upload_record)

    mock_upload_api.validate_upload.return_value = {
        "invalid_data": [{"row": 0, "errors": [{"field": "column", "error": "error"}]}]
    }

    response = client.post(
        "/account-statement/upload/upload-123/validate",
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    upload: ReconciliationFileUpload = ReconciliationFileUpload.filter(
        ReconciliationFileUpload.upload_id == "upload-123"
    )[0]
    assert upload.status == FileUploadStatus.SHEET_READY_TO_VALIDATE

    reconciliation = Reconciliation.get(str(_reconciliation.id))
    assert (
        reconciliation.status_bank_account_guids["account-123"]
        == BankAccountStatus.EXTRACT_IMPORTING
    )


@freeze_time("2025-01-01 12:00:00")
@pytest.mark.asyncio
async def test_update_account_statement_cannot_change_status(
    client, db_test, test_reconciliation, test_auth_client, mock_upload_api
):
    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(test_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.SHEET_PROCESSED,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": datetime.now().isoformat(),
    }
    ReconciliationFileUpload.create(**upload_record)

    mock_upload_api.validate_upload.return_value = {
        "id": "upload-123",
        "status": "SHEET_VALIDATED",
    }

    response = client.post(
        "/account-statement/upload/upload-123/validate",
    )

    assert response.status_code == status.HTTP_409_CONFLICT
    response = response.json()

    upload: ReconciliationFileUpload = ReconciliationFileUpload.filter(
        ReconciliationFileUpload.upload_id == "upload-123"
    )[0]
    assert upload.status == FileUploadStatus.SHEET_PROCESSED


@pytest.mark.asyncio
async def test_update_account_statement_not_found(
    client, db_test, test_reconciliation, test_auth_client, mock_upload_api
):
    response = client.post(
        "/account-statement/upload/non-existent-id/validate",
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_update_account_statement_invalid_account(
    client, db_test, test_reconciliation, test_auth_client, mock_upload_api
):
    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(test_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.UPLOADED,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": datetime.now().isoformat(),
    }
    ReconciliationFileUpload.create(**upload_record)

    response = client.post(
        "/account-statement/upload/upload-1234/validate",
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_update_account_statement_api_error(
    client, db_test, test_reconciliation, test_auth_client, mock_upload_api
):
    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(test_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.UPLOADED,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": datetime.now().isoformat(),
    }
    ReconciliationFileUpload.create(**upload_record)

    mock_upload_api.validate_upload.side_effect = requests.exceptions.HTTPError(
        "API Error"
    )

    response = client.post(
        "/account-statement/upload/upload-123/validate",
    )

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


@pytest.mark.asyncio
async def test_update_account_statement_generic_error(
    client, db_test, test_reconciliation, test_auth_client, mock_upload_api
):
    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(test_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.UPLOADED,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": datetime.now().isoformat(),
    }
    ReconciliationFileUpload.create(**upload_record)

    mock_upload_api.validate_upload.side_effect = Exception("error")

    response = client.post(
        "/account-statement/upload/upload-123/validate",
    )

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


@responses.activate
@pytest.mark.asyncio
async def test_get_account_statement_success(
    client, db_test, test_auth_client, mock_upload_api
):
    responses._add_from_file(
        file_path="tests/fixtures/get_statement_finished_transactions_api.yaml"
    )
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["12345"],
        status_bank_account_guids={"12345": BankAccountStatus.EXTRACT_IMPORTING},
    ).save()

    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(_reconciliation.id),
        "cockpit_account_id": "12345",
        "status": FileUploadStatus.UPLOADED,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": "2025-01-01T12:00:00",
    }
    ReconciliationFileUpload.create(**upload_record)

    mock_upload_api.get_account_statement_upload.return_value = {
        "id": "upload-123",
        "status": "FINISHED",
    }

    response = client.get(
        f"/account-statement/upload/upload-123?bank_account_guid=12345&reconciliation_id={str(_reconciliation.id)}"  # noqa
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    assert response_data == {
        "latest_uploads_by_account": {
            "12345": {
                "error_message": None,
                "status": FileUploadStatus.SHEET_PROCESSED,
                "upload_id": "upload-123",
                "metadata": {"upload_response": {"id": "upload-123"}},
            }
        },
        "status_bank_account_guids": {"12345": BankAccountStatus.PENDING_SUGGESTIONS},
    }


@pytest.mark.asyncio
async def test_get_account_statement_not_found(client, db_test, test_auth_client):
    response = client.get(
        "/account-statement/upload/non-existent-id?bank_account_guid=account-123&reconciliation_id=22"  # noqa
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_get_account_statement_api_error(
    client, db_test, test_auth_client, mock_upload_api
):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["account-123"],
        status_bank_account_guids={"account-123": BankAccountStatus.EXTRACT_IMPORTING},
    ).save()

    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.UPLOADED,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": "2025-01-01T12:00:00",
    }
    ReconciliationFileUpload.create(**upload_record)

    mock_upload_api.get_account_statement_upload.side_effect = Exception("API Error")

    response = client.get(
        f"/account-statement/upload/upload-123?bank_account_guid=account-123&reconciliation_id={str(_reconciliation.id)}"  # noqa
    )

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


@pytest.mark.asyncio
async def test_get_account_statement_with_pending_suggestions(
    client, db_test, test_reconciliation, test_auth_client, mock_upload_api
):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["account-123"],
        status_bank_account_guids={
            "account-123": BankAccountStatus.PENDING_SUGGESTIONS
        },
    ).save()

    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.PROCESSING,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": "2025-01-01T12:00:00",
        "processed_at": "2025-01-01T12:05:00",
    }
    ReconciliationFileUpload.create(**upload_record)

    mock_match_repo = AsyncMock()
    mock_match_repo.check_all_journal_headers_have_suggestions.return_value = True

    app.dependency_overrides[account_statement_routes.get_match_repository] = (
        lambda: mock_match_repo
    )

    response = client.get(
        f"/account-statement/upload/upload-123?bank_account_guid=account-123&reconciliation_id={str(_reconciliation.id)}"  # noqa
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    assert (
        response_data["status_bank_account_guids"]["account-123"]
        == BankAccountStatus.EXTRACT_IMPORTED
    )
    assert (
        response_data["latest_uploads_by_account"]["account-123"]["status"]
        == FileUploadStatus.SHEET_PROCESSED
    )


@pytest.mark.asyncio
async def test_get_account_statement_with_expired_pending_suggestions(
    client, db_test, test_auth_client, mock_upload_api
):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["account-123"],
        status_bank_account_guids={
            "account-123": BankAccountStatus.PENDING_SUGGESTIONS
        },
    ).save()

    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.PROCESSING,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": "2025-01-01T12:00:00",
        "processed_at": "2025-01-01T11:50:00",
    }
    ReconciliationFileUpload.create(**upload_record)

    response = client.get(
        f"/account-statement/upload/upload-123?bank_account_guid=account-123&reconciliation_id={str(_reconciliation.id)}"  # noqa
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    assert (
        response_data["status_bank_account_guids"]["account-123"]
        == BankAccountStatus.EXTRACT_IMPORTED
    )
    assert (
        response_data["latest_uploads_by_account"]["account-123"]["status"]
        == FileUploadStatus.SHEET_PROCESSED
    )


@pytest.mark.asyncio
async def test_get_account_statement_with_ready_to_validate_status(
    client, db_test, test_auth_client, mock_upload_api
):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["account-123"],
        status_bank_account_guids={"account-123": BankAccountStatus.EXTRACT_IMPORTING},
    ).save()

    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.UPLOADED,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": "2025-01-01T12:00:00",
    }
    ReconciliationFileUpload.create(**upload_record)

    mock_upload_api.get_account_statement_upload.return_value = {
        "id": "upload-123",
        "status": "SHEET_READY_TO_VALIDATE",
        "sheets_url": "link",
    }

    response = client.get(
        f"/account-statement/upload/upload-123?bank_account_guid=account-123&reconciliation_id={str(_reconciliation.id)}"  # noqa
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    assert (
        response_data["latest_uploads_by_account"]["account-123"]["metadata"][
            "sheets_url"
        ]
        == "link"
    )
    assert (
        response_data["latest_uploads_by_account"]["account-123"]["status"]
        == FileUploadStatus.SHEET_READY_TO_VALIDATE
    )
    assert (
        response_data["status_bank_account_guids"]["account-123"]
        == BankAccountStatus.EXTRACT_IMPORTING
    )


@pytest.mark.asyncio
async def test_get_account_statement_with_error_status(
    client, db_test, test_auth_client, mock_upload_api
):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["account-123"],
        status_bank_account_guids={"account-123": BankAccountStatus.EXTRACT_IMPORTING},
    ).save()

    upload_record = {
        "upload_id": "upload-123",
        "reconciliation_id": str(_reconciliation.id),
        "cockpit_account_id": "account-123",
        "status": FileUploadStatus.SHEET_READY_TO_VALIDATE,
        "file_type": "pdf",
        "_metadata": {"upload_response": {"id": "upload-123"}},
        "uploaded_at": "2025-01-01T12:00:00",
    }
    ReconciliationFileUpload.create(**upload_record)

    mock_upload_api.get_account_statement_upload.return_value = {
        "id": "upload-123",
        "status": "ERROR",
        "error": "Processing failed",
    }

    response = client.get(
        f"/account-statement/upload/upload-123?bank_account_guid=account-123&reconciliation_id={str(_reconciliation.id)}"  # noqa
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    assert (
        response_data["latest_uploads_by_account"]["account-123"]["status"]
        == FileUploadStatus.FAILED
    )
    assert (
        response_data["status_bank_account_guids"]["account-123"]
        == BankAccountStatus.EXTRACT_PROCESSING_ERROR
    )
