import uuid
from datetime import datetime
from decimal import Decimal
from unittest.mock import patch

import responses

from domain.exceptions import ReconciliationStatusException
from domain.models import (
    AccountConfiguration,
    AccountConfigurationTypeEnum,
    BankAccountStatus,
    JournalHeader,
    JournalLine,
    PartnerEntity,
    Reconciliation,
    ReconciliationStatusEnum,
    TransactionStatusEnum,
)
from shared.auth import Session


def test_set_reconciliation_status_active(client, db_test, test_auth_client):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    response = client.post(
        "/reconciliation/status",
        json={"id": reconciliation.id.__str__(), "status": "ACTIVE"},
    )
    assert response.status_code == 200
    assert response.json()["status"] == "ACTIVE"


def test_set_reconciliation_status_draft_to_active(client, db_test, test_auth_client):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
    )
    reconciliation.save()

    with patch(
        "application.reconciliation_service.camunda_service.send_correlate_message"
    ) as mock_send:
        mock_send.return_value = None
        response = client.post(
            "/reconciliation/status",
            json={"id": str(reconciliation.id), "status": "ACTIVE"},
        )
    assert response.status_code == 200
    assert response.json()["status"] == "ACTIVE"


def test_set_reconciliation_status_draft_to_finished_bank_status(
    client, db_test, test_auth_client
):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="679",
        operator="Operator B",
        status_bank_account_guids={"cockpit-id": BankAccountStatus.EXTRACT_IMPORTED},
        journal_headers=[
            JournalHeader(
                amount=Decimal("-1000.50"),
                date=datetime(2024, 3, 15),
                description="Sample Journal Header",
                notes="Test notes",
                status=TransactionStatusEnum.RECONCILED,
                partner_entity=PartnerEntity(
                    cpf_cnpj="**************", name="Fornecedor ABC"
                ),
                journal_lines=[
                    JournalLine(
                        line_type="DEBIT",
                        account_id="101",
                        amount=Decimal("-1000.50"),
                    ),
                    JournalLine(
                        line_type="CREDIT",
                        account_id="201",
                        amount=Decimal("1000.50"),
                    ),
                ],
                source_reference_id=str(uuid.uuid4()),
                cockpit_account_id=str(uuid.uuid4()),
            )
        ],
    )
    reconciliation.save()

    with patch(
        "application.reconciliation_service.camunda_service.send_correlate_message"
    ) as mock_send:
        mock_send.return_value = None
        response = client.post(
            "/reconciliation/status",
            json={"id": str(reconciliation.id), "status": "FINISHED"},
        )
    assert response.status_code == 200
    assert response.json()["status"] == "FINISHED"


def test_set_reconciliation_status_draft_to_finished_bank_status_exception(
    client, db_test, test_auth_client
):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="679",
        operator="Operator B",
        status_bank_account_guids={
            "cockpit-id": BankAccountStatus.NO_OPERATOR_FEEDBACK,
            "cockpit-id-2": BankAccountStatus.EXTRACT_IMPORTED,
        },
        journal_headers=[
            JournalHeader(
                amount=Decimal("-1000.50"),
                date=datetime(2024, 3, 15),
                description="Sample Journal Header",
                notes="Test notes",
                status=TransactionStatusEnum.RECONCILED,
                partner_entity=PartnerEntity(
                    cpf_cnpj="**************", name="Fornecedor ABC"
                ),
                journal_lines=[
                    JournalLine(
                        line_type="DEBIT",
                        account_id="101",
                        amount=Decimal("-1000.50"),
                    ),
                    JournalLine(
                        line_type="CREDIT",
                        account_id="201",
                        amount=Decimal("1000.50"),
                    ),
                ],
                source_reference_id=str(uuid.uuid4()),
                cockpit_account_id=str(uuid.uuid4()),
            )
        ],
    )
    reconciliation.save()

    with patch(
        "application.reconciliation_service.camunda_service.send_correlate_message"
    ) as mock_send:
        mock_send.return_value = None
        response = client.post(
            "/reconciliation/status",
            json={"id": str(reconciliation.id), "status": "FINISHED"},
        )
    assert response.status_code == 409


def test_set_reconciliation_status_draft_to_finished_header_exception(
    client, db_test, test_auth_client
):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="679",
        operator="Operator B",
        status_bank_account_guids={
            "cockpit-id": BankAccountStatus.EXTRACT_IMPORTED,
            "cockpit-id-2": BankAccountStatus.EXTRACT_IMPORTED,
        },
        journal_headers=[
            JournalHeader(
                amount=Decimal("-1000.50"),
                date=datetime(2024, 3, 15),
                description="Sample Journal Header",
                notes="Test notes",
                status=TransactionStatusEnum.NEW,
                partner_entity=PartnerEntity(
                    cpf_cnpj="**************", name="Fornecedor ABC"
                ),
                journal_lines=[
                    JournalLine(
                        line_type="DEBIT",
                        account_id="101",
                        amount=Decimal("-1000.50"),
                    ),
                    JournalLine(
                        line_type="CREDIT",
                        account_id="201",
                        amount=Decimal("1000.50"),
                    ),
                ],
                source_reference_id=str(uuid.uuid4()),
                cockpit_account_id=str(uuid.uuid4()),
            )
        ],
    )
    reconciliation.save()

    with patch(
        "application.reconciliation_service.camunda_service.send_correlate_message"
    ) as mock_send:
        mock_send.return_value = None
        response = client.post(
            "/reconciliation/status",
            json={"id": str(reconciliation.id), "status": "FINISHED"},
        )
    assert response.status_code == 409


def test_set_reconciliation_status_not_found(client, db_test, test_auth_client):
    with patch(
        "application.reconciliation_service.camunda_service.send_correlate_message"
    ) as mock_send:
        mock_send.return_value = None
        response = client.post(
            "/reconciliation/status",
            json={"id": "not-found", "status": "FINISHED"},
        )
    assert response.status_code == 404


def test_set_reconciliation_bank_status_not_found(client, db_test, test_auth_client):
    response = client.post(
        "/reconciliation/bank-account-status",
        json={
            "id": "not-found",
            "bank_account_status": {
                "cockpit-account": BankAccountStatus.FINISH_WITHOUT_EXTRACT
            },
        },
    )
    assert response.status_code == 404


def test_set_reconciliation_bank_status(client, db_test, test_auth_client):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="679",
        operator="Operator B",
        status_bank_account_guids={
            "cockpit-account": BankAccountStatus.NO_OPERATOR_FEEDBACK
        },
    )
    reconciliation.save()

    response = client.post(
        "/reconciliation/bank-account-status",
        json={
            "id": str(reconciliation.id),
            "bank_account_status": {
                "cockpit-account": BankAccountStatus.FINISH_WITHOUT_EXTRACT
            },
        },
    )
    assert response.status_code == 200
    assert response.json() == {
        "cockpit-account": BankAccountStatus.FINISH_WITHOUT_EXTRACT
    }


def test_set_reconciliation_bank_status_not_found_client(
    client, db_test, test_auth_client
):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
    )
    reconciliation.save()

    response = client.post(
        "/reconciliation/bank-account-status",
        json={
            "id": str(reconciliation.id),
            "bank_account_status": {
                "cockpit-account": BankAccountStatus.FINISH_WITHOUT_EXTRACT
            },
        },
    )
    assert response.status_code == 409


def test_set_reconciliation_status_draft_to_cancelled(
    client, db_test, test_auth_client
):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
    )
    reconciliation.save()

    with patch(
        "application.reconciliation_service.camunda_service.send_correlate_message"
    ) as mock_send:
        mock_send.return_value = None
        response = client.post(
            "/reconciliation/status",
            json={"id": str(reconciliation.id), "status": "CANCELLED"},
        )

    assert response.status_code == 200
    assert response.json()["status"] == "CANCELLED"


def test_set_reconciliation_status_draft_to_finished(client, db_test, test_auth_client):
    reconciliation = Reconciliation(status=ReconciliationStatusEnum.DRAFT).save()

    response = client.post(
        "/reconciliation/status",
        json={"id": reconciliation.id.__str__(), "status": "FINISHED"},
    )

    assert response.status_code == 409


def test_set_reconciliation_status_invalid_status(client, db_test):
    reconciliation = Reconciliation(status="FINISHED").save()
    invalid_reconciliation = {
        "id": reconciliation.id.__str__(),
        "status": "INVALID_STATUS",  # Use an invalid status to trigger validation
    }
    response = client.post("/reconciliation/status", json=invalid_reconciliation)
    assert response.status_code == 422  # Unprocessable Entity


def test_set_reconciliation_status_exception(mocker, client, db_test):
    reconciliation = Reconciliation(status="FINISHED").save()
    mocker.patch(
        "application.reconciliation_service.set_reconciliation_status",
        side_effect=ReconciliationStatusException,
    )

    reconciliation = {"id": reconciliation.id.__str__(), "status": "ACTIVE"}
    response = client.post("/reconciliation/status", json=reconciliation)
    assert response.status_code == 409


def mock_get_current_session():
    return Session(
        user_id="123", email="<EMAIL>", name="test user", token="Bearer token"
    )


def test_download_files_not_found(client, db_test, mocker, test_auth_client):
    response = client.get("/reconciliation/download-files/not-found")
    assert response.status_code == 404


def test_download_files_not_finished(client, db_test, mocker, test_auth_client):
    reconciliation = Reconciliation(
        competence="2023-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="456",
        operator="<EMAIL>",
    ).save()

    response = client.get(f"/reconciliation/download-files/{str(reconciliation.id)}")
    assert response.status_code == 400


def test_download_files(client, db_test, mocker, test_auth_client):
    reconciliation = Reconciliation(
        competence="2023-10",
        status=ReconciliationStatusEnum.FINISHED,
        cockpit_customer_id="456",
        operator="<EMAIL>",
    ).save()

    journal_header = JournalHeader(
        amount=300.00,
        date=datetime.now(),
        description="Sample Journal Header",
        notes="Test notes",
        status="NEW",
        reconciliation_id=reconciliation.id,
        _metadata={},
    ).save()

    JournalLine(
        line_type="DEBIT",
        amount=300.00,
        description="Sample Journal Line",
        account_id=uuid.uuid4(),
        journal_header_id=journal_header.id,
        _metadata={},
    ).save()

    JournalLine(
        line_type="CREDIT",
        amount=300.00,
        description="Sample Journal Line",
        account_id=uuid.uuid4(),
        journal_header_id=journal_header.id,
        _metadata={},
    ).save()

    mock_cockpit_client = mocker.patch(
        "application.strategies.excel.excel_generation_strategy.CockpitApiClient"
    )
    mock_cockpit_client.return_value.get_customer.return_value = {
        "cnpj": "12.345.678/0001-90"
    }

    response = client.get(
        f"/reconciliation/download-files/{reconciliation.id.__str__()}"
    )

    assert response.status_code == 200
    response_data = response.json()

    assert "excel" in response_data
    assert "txt" in response_data

    response = client.get(
        f"/reconciliation/download-files/{reconciliation.id.__str__()}"
    )

    assert response.status_code == 200
    response_data = response.json()

    assert "excel" in response_data
    assert "txt" in response_data


def test_download_files_without_journal(client, db_test, mocker, test_auth_client):
    reconciliation = Reconciliation(
        competence="2023-10",
        status=ReconciliationStatusEnum.FINISHED,
        cockpit_customer_id="456",
        operator="<EMAIL>",
    ).save()

    mock_cockpit_client = mocker.patch(
        "application.strategies.excel.excel_generation_strategy.CockpitApiClient"
    )
    mock_cockpit_client.return_value.get_customer.return_value = {
        "cnpj": "12.345.678/0001-90"
    }

    response = client.get(
        f"/reconciliation/download-files/{reconciliation.id.__str__()}"
    )

    assert response.status_code == 200
    response_data = response.json()

    assert "excel" in response_data
    assert "txt" in response_data

    response = client.get(
        f"/reconciliation/download-files/{reconciliation.id.__str__()}"
    )

    assert response.status_code == 200
    response_data = response.json()

    assert "excel" in response_data
    assert "txt" in response_data


@responses.activate
@patch("application.draft_reconciliation_service.CamundaService")
def test_create_draft_reconciliation(
    mock_CamundaService, client, db_test, mocker, test_auth_client
):
    responses._add_from_file(file_path="tests/fixtures/transactions_api.yaml")
    responses._add_from_file(file_path="tests/fixtures/cockpit_api.yaml")

    mock_camunda = mock_CamundaService.return_value
    mock_camunda.start_process.return_value = "process_123"

    response = client.post(
        "/reconciliation/draft",
        json={
            "cockpit_customer_id": "3e5756e5-e607-4f73-a562-b372b97de041",
            "competence": "2024-09",
            "bank_account_guids": [
                "37d0873a-e07a-11ee-ba2b-0a7ea7812cad",
                "3756e5-e607-4f73-a562-b372b97de041",
            ],
        },
    )

    assert response.status_code == 200
    data = response.json()
    reconciliation_id = data["id"]

    assert reconciliation_id is not None

    assert "transactions" in data
    assert len(data["transactions"]) > 0

    header = data["transactions"][0]
    assert "amount" in header
    assert "journal_lines" in header
    assert len(header["journal_lines"]) > 0

    assert data["status_bank_account_guids"] == {
        "37d0873a-e07a-11ee-ba2b-0a7ea7812cad": "EXTRACT_IMPORTED",
        "3756e5-e607-4f73-a562-b372b97de041": "NO_OPERATOR_FEEDBACK",
    }


@responses.activate
@patch("application.draft_reconciliation_service.CamundaService")
def test_create_draft_reconciliation_without_bank_accounts(
    mock_CamundaService, client, db_test, mocker, test_auth_client
):
    responses._add_from_file(file_path="tests/fixtures/transactions_api.yaml")
    responses._add_from_file(file_path="tests/fixtures/cockpit_api.yaml")

    mock_camunda = mock_CamundaService.return_value
    mock_camunda.start_process.return_value = "process_123"

    response = client.post(
        "/reconciliation/draft",
        json={
            "cockpit_customer_id": "3e5756e5-e607-4f73-a562-b372b97de041",
            "competence": "2024-09",
            "bank_account_guids": [],
        },
    )

    assert response.status_code == 200
    data = response.json()
    reconciliation_id = data["id"]

    assert reconciliation_id is not None
    assert data["status_bank_account_guids"] == {}


@responses.activate
@patch("application.draft_reconciliation_service.CamundaService")
def test_create_draft_reconciliation_with_account_configuration(
    mock_CamundaService, client, db_test, mocker, test_auth_client
):
    responses._add_from_file(file_path="tests/fixtures/transactions_api.yaml")
    responses._add_from_file(file_path="tests/fixtures/cockpit_api.yaml")

    mock_camunda = mock_CamundaService.return_value
    mock_camunda.start_process.return_value = "process_123"

    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        customer_cnpj="**************",
        operator="Operator A",
    ).save()

    AccountConfiguration(
        cockpit_account_id="3e5756e5-e607-4f73-a562-b372b97de041",
        value="BANK_CODE_1",
        type=AccountConfigurationTypeEnum.CHART_OF_ACCOUNTS_CODE,
        reconciliation_id=reconciliation.id,
    ).save()

    AccountConfiguration(
        cockpit_account_id="3e5756e5-e607-4f73-a562-b372b97de041",
        value="BANK_CODE",
        type=AccountConfigurationTypeEnum.CHART_OF_ACCOUNTS_CODE,
        reconciliation_id=reconciliation.id,
    ).save()

    response = client.post(
        "/reconciliation/draft",
        json={
            "cockpit_customer_id": "3e5756e5-e607-4f73-a562-b372b97de041",
            "competence": "2024-09",
            "bank_account_guids": ["37d0873a-e07a-11ee-ba2b-0a7ea7812cad"],
        },
    )

    assert response.status_code == 200
    data = response.json()
    reconciliation_id = data["id"]

    assert reconciliation_id is not None
    assert "transactions" in data
    assert len(data["transactions"]) > 0

    header = data["transactions"][0]
    assert "amount" in header
    assert "journal_lines" in header
    assert len(header["journal_lines"]) > 0


@responses.activate
@patch("application.draft_reconciliation_service.CamundaService")
@patch("infrastructure.repositories.sqs_repository.SqsRepository")
def test_sync_add_transactions_to_reconciliation(
    mock_SqsRepository, mock_CamundaService, client, db_test, mocker, test_auth_client
):
    mock_sqs = mock_SqsRepository.return_value
    mock_sqs.send_message.return_value = {"MessageId": "test-message-id"}

    mock_camunda = mock_CamundaService.return_value
    mock_camunda.sqs = mock_sqs

    reconciliation = Reconciliation(
        competence="2024-09",
        status="DRAFT",
        cockpit_customer_id="12345",
        operator="<EMAIL>",
        orchestrate_process_id="process_123",
    ).save()

    responses._add_from_file(file_path="tests/fixtures/transactions_api.yaml")

    response = client.post(
        "/reconciliation/draft",
        json={
            "cockpit_customer_id": "12345",
            "competence": "2024-09",
            "bank_account_guids": ["37d0873a-e07a-11ee-ba2b-0a7ea7812cad"],
        },
    )

    assert response.status_code == 200
    data = response.json()
    assert str(reconciliation.id) == data["id"]


def test_get_reconciliation_not_found(client, db_test, test_auth_client):
    fake_uuid = "********-1234-5678-9012-************"

    response = test_auth_client.get(
        f"/reconciliation/{fake_uuid}",
    )

    assert response.status_code == 404


@responses.activate
@patch("application.draft_reconciliation_service.CamundaService")
@patch("infrastructure.repositories.sqs_repository.SqsRepository")
def test_sync_add_journals_to_reconciliation(
    mock_SqsRepository, mock_CamundaService, client, db_test, mocker, test_auth_client
):
    mock_sqs = mock_SqsRepository.return_value
    mock_sqs.send_message.return_value = {"MessageId": "test-message-id"}

    mock_camunda = mock_CamundaService.return_value
    mock_camunda.sqs = mock_sqs

    mock_session = mocker.Mock()
    mocker.patch("fastapi.Request.state", mock_session)
    reconciliation = Reconciliation(
        competence="2024-09",
        status="DRAFT",
        cockpit_customer_id="12345",
        operator="<EMAIL>",
        orchestrate_process_id="process_123",
        bank_account_guids=["37d0873a-e07a-11ee-ba2b-0a7ea7812cad"],
    ).save()

    responses._add_from_file(file_path="tests/fixtures/transactions_api.yaml")

    assert len(reconciliation.journal_headers) == 0
    response = client.post(
        "/reconciliation/draft",
        json={
            "cockpit_customer_id": "12345",
            "competence": "2024-09",
            "bank_account_guids": ["37d0873a-e07a-11ee-ba2b-0a7ea7812cad"],
        },
    )

    assert response.status_code == 200
    data = response.json()
    assert str(reconciliation.id) == data["id"]
    assert len(reconciliation.journal_headers) == 3
    assert data["status_bank_account_guids"] == {
        "37d0873a-e07a-11ee-ba2b-0a7ea7812cad": "EXTRACT_IMPORTED"
    }


@responses.activate
def test_sync_remove_journals_from_reconciliation(
    client, db_test, mocker, test_auth_client
):
    mock_session = mocker.Mock()
    mocker.patch("fastapi.Request.state", mock_session)
    reconciliation = Reconciliation(
        competence="2024-09",
        status="DRAFT",
        cockpit_customer_id="12345",
        operator="<EMAIL>",
        orchestrate_process_id="process_123",
        bank_account_guids=["37d0873a-e07a-11ee-ba2b-0a7ea7812cad"],
        journal_headers=[
            JournalHeader(
                amount=100.00,
                date="2024-09-01 00:00:00",
                description="Test Transaction",
                notes="Test Notes",
                status="RECONCILED",
                cockpit_account_id="37d0873a-e07a-11ee-ba2b-0a7ea7812cad",
                source_reference_id="6a8468fb-1ea2-4187-87ca-a2ddaea7b537",
            )
        ],
    ).save()

    responses._add_from_file(file_path="tests/fixtures/transactions_api.yaml")
    assert len(reconciliation.journal_headers) == 1
    response = client.post(
        "/reconciliation/draft",
        json={
            "cockpit_customer_id": "12345",
            "competence": "2024-09",
            "bank_account_guids": ["37d0873a-e07a-11ee-ba2b-0a7ea7812cad"],
        },
    )

    assert response.status_code == 200
    data = response.json()
    assert str(reconciliation.id) == data["id"]
    assert len(reconciliation.journal_headers) == 3
    assert data["status_bank_account_guids"] == {
        "37d0873a-e07a-11ee-ba2b-0a7ea7812cad": "EXTRACT_IMPORTED"
    }


@responses.activate
def test_sync_cannot_if_finished_reconciliation(
    client, db_test, mocker, test_auth_client
):
    mock_session = mocker.Mock()
    mocker.patch("fastapi.Request.state", mock_session)
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.FINISHED,
        cockpit_customer_id="12345",
        operator="<EMAIL>",
        orchestrate_process_id="process_123",
        bank_account_guids=["37d0873a-e07a-11ee-ba2b-0a7ea7812cad"],
        journal_headers=[
            JournalHeader(
                amount=100.00,
                date="2024-09-01 00:00:00",
                description="Test Transaction",
                notes="Test Notes",
                status="RECONCILED",
                cockpit_account_id="37d0873a-e07a-11ee-ba2b-0a7ea7812cad",
                source_reference_id="6a8468fb-1ea2-4187-87ca-a2ddaea7b537",
            )
        ],
    ).save()

    responses._add_from_file(file_path="tests/fixtures/transactions_api.yaml")
    assert len(reconciliation.journal_headers) == 1
    response = client.post(
        "/reconciliation/draft",
        json={
            "cockpit_customer_id": "12345",
            "competence": "2024-09",
            "bank_account_guids": ["37d0873a-e07a-11ee-ba2b-0a7ea7812cad"],
        },
    )

    assert response.status_code == 200
    data = response.json()
    assert str(reconciliation.id) == data["id"]
    assert len(reconciliation.journal_headers) == 1
    assert data["status_bank_account_guids"] == {
        "37d0873a-e07a-11ee-ba2b-0a7ea7812cad": "EXTRACT_IMPORTED"
    }


@responses.activate
def test_sync_bank_account_guids_reconciliation(
    client, db_test, mocker, test_auth_client
):
    mock_session = mocker.Mock()
    mocker.patch("fastapi.Request.state", mock_session)
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="12345",
        operator="<EMAIL>",
        orchestrate_process_id="process_123",
        bank_account_guids=["37d0873a-e07a-11ee-ba2b-0a7ea7812cad"],
        journal_headers=[
            JournalHeader(
                amount=100.00,
                date="2024-09-01 00:00:00",
                description="Test Transaction",
                notes="Test Notes",
                status="RECONCILED",
                cockpit_account_id="37d0873a-e07a-11ee-ba2b-0a7ea7812cad",
                source_reference_id="6a8468fb-1ea2-4187-87ca-a2ddaea7b537",
            )
        ],
    ).save()

    responses._add_from_file(file_path="tests/fixtures/transactions_multiple_api.yaml")
    assert len(reconciliation.journal_headers) == 1
    response = client.post(
        "/reconciliation/draft",
        json={
            "cockpit_customer_id": "12345",
            "competence": "2024-09",
            "bank_account_guids": [
                "37d0873a-e07a-11ee-ba2b-0a7ea7812cad",
                "38d0873a-e07a-11ee-ba2b-0a7ea7812cad",
            ],
        },
    )

    assert response.status_code == 200
    data = response.json()
    assert str(reconciliation.id) == data["id"]
    assert reconciliation.bank_account_guids == [
        "37d0873a-e07a-11ee-ba2b-0a7ea7812cad",
        "38d0873a-e07a-11ee-ba2b-0a7ea7812cad",
    ]
    assert len(reconciliation.journal_headers) == 4
    assert data["status_bank_account_guids"] == {
        "37d0873a-e07a-11ee-ba2b-0a7ea7812cad": "EXTRACT_IMPORTED",
        "38d0873a-e07a-11ee-ba2b-0a7ea7812cad": "EXTRACT_IMPORTED",
    }
