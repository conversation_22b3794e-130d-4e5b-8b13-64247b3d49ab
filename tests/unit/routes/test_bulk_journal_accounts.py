from datetime import datetime

import pytest

from domain.models import (
    JournalHeader,
    JournalLine,
    OperationTypeEnum,
    Reconciliation,
    ReconciliationStatusEnum,
    TransactionStatusEnum,
)


@pytest.fixture
def active_reconciliation(db_test):
    return Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
    ).save()


@pytest.fixture
def inactive_reconciliation(db_test):
    return Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.FINISHED,
        cockpit_customer_id="456",
        operator="Operator A",
    ).save()


@pytest.fixture
def journal_header_with_lines(active_reconciliation):
    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        cockpit_account_id="123",
        description="Test Journal Header",
        notes="Test notes",
        status=TransactionStatusEnum.NEW,
        reconciliation_id=active_reconciliation.id,
        source_reference_id="876",
        _metadata={"test": "teste1"},
    )
    journal_header.save()

    lines = []

    debit_line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=50.00,
        description="Debit Entry 1",
        notes="Test notes",
        account_id="old-account-1",
        journal_header_id=journal_header.id,
    )
    debit_line.save()
    lines.append(debit_line)

    debit_line2 = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=50.00,
        description="Debit Entry 2",
        notes="Test notes",
        account_id="old-account-2",
        journal_header_id=journal_header.id,
    )
    debit_line2.save()
    lines.append(debit_line2)

    credit_line = JournalLine(
        line_type=OperationTypeEnum.CREDIT,
        amount=100.00,
        description="Credit Entry",
        notes="Test notes",
        account_id="old-credit-account",
        journal_header_id=journal_header.id,
    )
    credit_line.save()
    lines.append(credit_line)

    return journal_header, lines


@pytest.fixture
def journal_header_with_inactive_reconciliation(inactive_reconciliation):
    journal_header = JournalHeader(
        amount=200.00,
        date=datetime.now(),
        cockpit_account_id="456",
        description="Test Journal Header Inactive",
        notes="Test notes",
        status=TransactionStatusEnum.RECONCILED,
        reconciliation_id=inactive_reconciliation.id,
        source_reference_id="987",
        _metadata={"test": "teste2"},
    )
    journal_header.save()

    line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=200.00,
        description="Debit Entry Inactive",
        notes="Test notes",
        account_id="inactive-account",
        journal_header_id=journal_header.id,
    )
    line.save()

    return journal_header, [line]


def test_bulk_update_journal_line_accounts_success(
    test_auth_client, db_test, journal_header_with_lines
):
    """Test successful bulk update of multiple journal lines"""

    _, lines = journal_header_with_lines
    line_ids = [str(line.id) for line in lines]

    response = test_auth_client.patch(
        "/transactions/journal-lines/accounts",
        json={
            "journal_line_ids": line_ids,
            "updated_data": {"account_id": "new-bulk-account"},
        },
    )

    assert response.status_code == 200
    results = response.json()
    assert len(results) == 3

    for line_id in line_ids:
        updated_line = JournalLine.get(line_id)
        assert updated_line.account_id == "new-bulk-account"


def test_bulk_update_journal_line_accounts_inactive_reconciliation(
    test_auth_client,
    db_test,
    journal_header_with_lines,
    journal_header_with_inactive_reconciliation,
):
    """Test bulk update fails when some lines are in inactive reconciliations"""

    _, active_lines = journal_header_with_lines
    _, inactive_lines = journal_header_with_inactive_reconciliation

    all_line_ids = [str(line.id) for line in active_lines + inactive_lines]

    response = test_auth_client.patch(
        "/transactions/journal-lines/accounts",
        json={
            "journal_line_ids": all_line_ids,
            "updated_data": {"account_id": "should-not-update"},
        },
    )

    assert response.status_code == 400
    assert "Cannot update journal lines in headers" in response.json()["detail"]
    assert "Status: FINISHED" in response.json()["detail"]

    for line in active_lines:
        assert JournalLine.get(line.id).account_id != "should-not-update"

    for line in inactive_lines:
        assert JournalLine.get(line.id).account_id != "should-not-update"
