import threading
import time
from datetime import datetime
from unittest.mock import patch
from uuid import uuid4

import pytest
from sqlalchemy.exc import SQLAlchemyError

from domain.models import (
    JournalHeader,
    JournalHeaderStatusEnum,
    JournalLine,
    LinkedInvoice,
    OperationTypeEnum,
    PartnerEntity,
    Reconciliation,
    ReconciliationStatusEnum,
    TransactionStatusEnum,
)
from infrastructure.repositories.journal_repository import JournalRepository
from shared.session_manager import session_scope


@pytest.fixture
def reconciliation(db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
        customer_cnpj="*************",
    )
    reconciliation.save()
    return reconciliation


@pytest.fixture
def journal_header_income(reconciliation):
    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        cockpit_account_id="123",
        description="Sample Income Transaction",
        notes="Test notes",
        status=TransactionStatusEnum.NEW,
        reconciliation_id=reconciliation.id,
        source_reference_id="876",
        _metadata={"test": "teste1"},
    )
    journal_header.save()
    return journal_header


@pytest.fixture
def journal_header_expense(reconciliation):
    journal_header = JournalHeader(
        amount=-100.00,
        date=datetime.now(),
        cockpit_account_id="123",
        description="Sample Expense Transaction",
        notes="Test notes",
        status=TransactionStatusEnum.NEW,
        reconciliation_id=reconciliation.id,
        source_reference_id="876",
        _metadata={"test": "teste1"},
    )
    journal_header.save()
    return journal_header


@pytest.fixture
def journal_lines_income(journal_header_income):
    debit_line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        description="Sample Debit Entry",
        notes="Test notes",
        account_id=None,
        journal_header_id=journal_header_income.id,
    )
    debit_line.save()

    credit_line = JournalLine(
        line_type=OperationTypeEnum.CREDIT,
        amount=100.00,
        description="Sample Credit Entry",
        notes="Test notes",
        account_id="Account 1",
        journal_header_id=journal_header_income.id,
    )
    credit_line.save()
    return debit_line, credit_line


@pytest.fixture
def journal_lines_expense(journal_header_expense):
    debit_line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=-100.00,
        description="Sample Debit Entry",
        notes="Test notes",
        account_id="Account 2",
        journal_header_id=journal_header_expense.id,
    )
    debit_line.save()

    credit_line = JournalLine(
        line_type=OperationTypeEnum.CREDIT,
        amount=-100.00,
        description="Sample Credit Entry",
        notes="Test notes",
        account_id=None,
        journal_header_id=journal_header_expense.id,
    )
    credit_line.save()

    return debit_line, credit_line


def test_edit_multiple_journal_entries(
    test_auth_client,
    db_test,
    journal_lines_income,
    journal_lines_expense,
):
    all_lines = list(journal_lines_income) + list(journal_lines_expense)
    journal_line_ids = [str(line.id) for line in all_lines if line.id]

    response = test_auth_client.patch(
        "/transactions/journal-lines/accounts",
        json={
            "journal_line_ids": journal_line_ids,
            "updated_data": {"account_id": "bulk_account_id_updated"},
        },
    )

    assert response.status_code == 200
    for line_id in journal_line_ids:
        updated_line = JournalLine.get(line_id)
        assert updated_line.account_id == "bulk_account_id_updated"


def test_bulk_update_journal_line_accounts_unexpected_error(
    monkeypatch, test_auth_client, db_test, journal_lines_income
):
    # Simula erro inesperado no select
    def mock_execute(*args, **kwargs):
        raise Exception("Simulated unexpected error")

    monkeypatch.setattr(
        "infrastructure.repositories.journal_repository.session_scope",
        lambda *a, **k: None,
    )
    monkeypatch.setattr("sqlalchemy.orm.Session.execute", mock_execute)

    line_ids = [str(line.id) for line in journal_lines_income]
    response = test_auth_client.patch(
        "/transactions/journal-lines/accounts",
        json={
            "journal_line_ids": line_ids,
            "updated_data": {"account_id": "bulk_account_id_updated"},
        },
    )
    assert response.status_code == 400
    assert "Error updating journal line accounts" in response.json()["detail"]


def test_change_journal_status(test_auth_client, db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        cockpit_account_id="123",
        description="Sample Income Transaction",
        notes="Test notes",
        status=TransactionStatusEnum.NEW,
        reconciliation_id=reconciliation.id,
        source_reference_id="876",
        _metadata={"test": "teste1"},
    )
    journal_header.save()

    debit_line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        description="Sample Debit Entry",
        notes="Test notes",
        account_id="Account 2",
        journal_header_id=journal_header.id,
    )
    debit_line.save()

    credit_line = JournalLine(
        line_type=OperationTypeEnum.CREDIT,
        amount=100.00,
        description="Sample Credit Entry",
        notes="Test notes",
        account_id="Account 1",
        journal_header_id=journal_header.id,
    )
    credit_line.save()

    response = test_auth_client.post(f"/transactions/{journal_header.id}/status")
    assert response.status_code == 200

    got_journal_header = JournalHeader.get(str(journal_header.id))
    assert got_journal_header.status == TransactionStatusEnum.RECONCILED


def test_update_existing_partner_name(
    test_auth_client, db_test, journal_header_expense
):
    """
    Test updating the name of an existing PartnerEntity linked to a JournalHeader.
    """
    existing_partner = PartnerEntity(cpf_cnpj="***********", name="Old Partner")
    existing_partner.save()
    new_name = "New Partner"

    journal_header_expense.partner_entity_id = existing_partner.id
    journal_header_expense.save()

    response = test_auth_client.patch(
        f"/transactions/journal-header/{journal_header_expense.id}",
        json={
            "partner_entity_name": new_name,
        },
    )

    assert response.status_code == 200
    result = response.json()
    assert result.get("trading_entity") is not None
    assert result["trading_entity"]["name"] == new_name

    updated_partner = PartnerEntity().filter_by(cpf_cnpj=existing_partner.cpf_cnpj)[0]
    assert updated_partner.name == new_name


def test_patch_journal_header_only_cpf_creates_new_partner_with_existing_name(
    test_auth_client, db_test, journal_header_expense
):
    """
    If only an existing CPF/CNPJ is sent, a new PartnerEntity must be created
    with the name from the database, linked to the header,
    and the old partner must not be linked.
    """
    existing_partner = PartnerEntity(cpf_cnpj="11122233344", name="Nome Original")
    existing_partner.save()

    journal_header_expense.partner_entity_id = None
    journal_header_expense.save()

    response = test_auth_client.patch(
        f"/transactions/journal-header/{journal_header_expense.id}",
        json={"partner_entity_cpf_cnpj": "11122233344"},
    )

    assert response.status_code == 200
    result = response.json()
    assert result.get("trading_entity") is not None
    assert result["trading_entity"]["cpf_cnpj"] == "11122233344"
    assert result["trading_entity"]["name"] == "Nome Original"

    # Deve existir dois partners com o mesmo CPF/CNPJ, mas IDs diferentes
    partners = PartnerEntity().filter_by(cpf_cnpj="11122233344")
    assert len(partners) == 2

    # O novo partner deve estar vinculado ao header
    assert journal_header_expense.partner_entity_id != existing_partner.id
    assert journal_header_expense.partner_entity.name == "Nome Original"
    assert journal_header_expense.partner_entity.cpf_cnpj == "11122233344"


def test_edit_journal_only_notes(
    test_auth_client, db_test, journal_header_expense, journal_lines_expense
):
    debit_line, credit_line = journal_lines_expense

    response = test_auth_client.patch(
        f"/transactions/journal-line/{debit_line.id}",
        json={
            "notes": "Updated notes",
        },
    )

    assert response.status_code == 200
    updated_debit = JournalLine.get(debit_line.id)
    updated_credit = JournalLine.get(credit_line.id)
    assert updated_debit.notes == "Updated notes"
    assert updated_credit.notes == credit_line.notes


def test_edit_journal_with_linked_invoices(
    test_auth_client, db_test, journal_header_expense, journal_lines_expense
):
    debit_line, credit_line = journal_lines_expense

    linked_invoice = LinkedInvoice(
        platform_invoice_id="invoice_123",
        link_date=datetime.now(),
        journal_line_id=debit_line.id,
    )
    linked_invoice.save()

    response = test_auth_client.patch(
        f"/transactions/journal-line/{debit_line.id}",
        json={
            "linked_invoices": [
                {
                    "platform_invoice_id": "invoice_456",
                    "link_date": datetime.now().isoformat(),
                }
            ],
            "should_link_invoices": True,
        },
    )

    assert response.status_code == 200
    assert response.json()["linked_invoices"][0]["platform_invoice_id"] == "invoice_456"

    linked_invoices = LinkedInvoice().filter_by(
        journal_line_id=str(debit_line.id), deleted=False
    )
    linked_invoices.sort(key=lambda invoice: invoice.platform_invoice_id)

    assert len(linked_invoices) == 1
    assert linked_invoices[0].platform_invoice_id == "invoice_456"


def test_delete_journal_line_success(test_auth_client, db_test, journal_lines_income):
    debit_line, credit_line = journal_lines_income
    header = debit_line.journal_header

    extra_debit = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        description="Extra Debit Line",
        notes="Extra",
        account_id="123",
        journal_header=header,
    )
    extra_debit.save()

    assert len([line for line in header.journal_lines if not line.deleted]) == 3

    response = test_auth_client.delete(f"/transactions/journal-line/{debit_line.id}")
    assert response.status_code == 200
    assert response.json()["message"] == "JournalLine deleted successfully"

    repo = JournalRepository()
    header = repo.get_journal_header(header.id)
    remaining = [line for line in header.journal_lines if not line.deleted]
    assert len(remaining) == 2

    types = [line.line_type for line in remaining]
    assert OperationTypeEnum.DEBIT in types
    assert OperationTypeEnum.CREDIT in types


expected_message_error_delete = (
    "Error: Cannot delete this JournalLine. A JournalHeader must contain "
    "at least one DEBIT and one CREDIT line."
)


def test_delete_journal_line_error(test_auth_client, db_test, journal_lines_income):
    debit_line, credit_line = journal_lines_income
    header = debit_line.journal_header

    extra_debit = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        description="Extra Debit Line",
        notes="Extra",
        account_id="123",
        journal_header=header,
    )
    extra_debit.save()

    assert len([line for line in header.journal_lines if not line.deleted]) == 3

    response = test_auth_client.delete(f"/transactions/journal-line/{credit_line.id}")
    assert response.status_code == 400
    assert response.json()["detail"] == expected_message_error_delete


def test_delete_journal_line_blocked_if_last_debit_or_credit(
    test_auth_client, db_test, journal_lines_income
):
    debit_line, credit_line = journal_lines_income

    response = test_auth_client.delete(f"/transactions/journal-line/{debit_line.id}")
    assert response.status_code == 400
    assert response.json()["detail"] == expected_message_error_delete


def test_delete_journal_line_not_found_returns_404(test_auth_client, db_test):
    invalid_id = "ffffffff-ffff-ffff-ffff-ffffffffffff"

    response = test_auth_client.delete(f"/transactions/journal-line/{invalid_id}")

    assert response.status_code == 404
    assert response.json()["detail"] == f"Error: Journal Line ID not found {invalid_id}"


def test_delete_journal_line_unexpected_error_returns_500(test_auth_client, db_test):
    # Without associated journal_header
    journal_line = JournalLine(
        line_type="DEBIT",
        amount=100.00,
        description="Line without header",
        notes="Should cause failure",
        account_id="fake-account",
    )
    journal_line.save()

    response = test_auth_client.delete(f"/transactions/journal-line/{journal_line.id}")

    assert response.status_code == 500
    assert response.json()["detail"] == "Unexpected error deleting JournalLine"


def test_patch_journal_line_success(test_auth_client, db_test, journal_lines_income):
    debit_line, credit_line = journal_lines_income

    # Test updating debit line with expense_account
    response = test_auth_client.patch(
        f"/transactions/journal-line/{debit_line.id}",
        json={
            "description": "Updated Description",
            "notes": "Updated Notes",
            "expense_account": "new_account_id",
            "line_type": "DEBIT",
        },
    )

    assert response.status_code == 200

    updated = JournalLine.get(debit_line.id)
    assert updated.description == "Updated Description"
    assert updated.notes == "Updated Notes"
    assert updated.account_id == "new_account_id"

    # Test updating credit line with income_account
    response = test_auth_client.patch(
        f"/transactions/journal-line/{credit_line.id}",
        json={
            "description": "Updated Credit Description",
            "notes": "Updated Credit Notes",
            "income_account": "new_income_account",
            "line_type": "CREDIT",
        },
    )

    assert response.status_code == 200

    updated = JournalLine.get(credit_line.id)
    assert updated.description == "Updated Credit Description"
    assert updated.notes == "Updated Credit Notes"
    assert updated.account_id == "new_income_account"


def test_delete_journal_line_returns_500_on_db_failure(
    test_auth_client, db_test, journal_lines_income
):
    debit_line, credit_line = journal_lines_income
    header = debit_line.journal_header

    extra_debit = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        description="Extra Debit",
        notes="Teste",
        account_id="123",
        journal_header=header,
    )
    extra_debit.save()

    # Simula falha no delete
    with patch.object(
        JournalLine, "delete", side_effect=SQLAlchemyError("Simulated DB failure")
    ):
        response = test_auth_client.delete(
            f"/transactions/journal-line/{debit_line.id}"
        )
        assert response.status_code == 500
        assert "Failed to delete Journal Line" in response.json()["detail"]
        assert "Simulated DB failure" in response.json()["detail"]


def test_delete_journal_line_inactive_reconciliation(test_auth_client, db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.FINISHED,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        reconciliation_id=reconciliation.id,
        status=JournalHeaderStatusEnum.NEW,
    )
    journal_header.save()

    debit_line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        journal_header_id=journal_header.id,
    )
    debit_line.save()

    credit_line = JournalLine(
        line_type=OperationTypeEnum.CREDIT,
        amount=100.00,
        journal_header_id=journal_header.id,
    )
    credit_line.save()

    response = test_auth_client.delete(f"/transactions/journal-line/{debit_line.id}")

    assert response.status_code == 400
    assert "Error: Cannot delete journal lines" in response.json()["detail"]
    assert "Current status: FINISHED" in response.json()["detail"]


def test_patch_journal_line_not_found_returns_404(test_auth_client, db_test):
    invalid_id = str(uuid4())
    response = test_auth_client.patch(
        f"/transactions/journal-line/{invalid_id}",
        json={"description": "Should not update"},
    )
    assert response.status_code == 404
    assert response.json()["detail"] == f"Error: Journal Line ID not found {invalid_id}"


def test_patch_journal_line_internal_error(
    monkeypatch, test_auth_client, db_test, journal_lines_income
):
    debit_line, _ = journal_lines_income

    def mock_save_fail(self):
        raise Exception("Mocked internal error")

    monkeypatch.setattr(JournalLine, "save", mock_save_fail)

    response = test_auth_client.patch(
        f"/transactions/journal-line/{debit_line.id}",
        json={"description": "Trigger Fail"},
    )

    assert response.status_code == 500
    assert response.json()["detail"] == "Unexpected error updating JournalLine"


def test_patch_journal_line_inactive_reconciliation(test_auth_client, db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.CANCELLED,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        reconciliation_id=reconciliation.id,
        status=JournalHeaderStatusEnum.NEW,
    )
    journal_header.save()

    journal_line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        journal_header_id=journal_header.id,
    )
    journal_line.save()

    response = test_auth_client.patch(
        f"/transactions/journal-line/{journal_line.id}",
        json={"description": "Should not update"},
    )

    assert response.status_code == 400
    assert "Error: Cannot update journal lines" in response.json()["detail"]
    assert "Current status: CANCELLED" in response.json()["detail"]


def test_create_journal_line_success(test_auth_client, db_test, journal_header_income):
    response = test_auth_client.post(
        f"/transactions/journal-line/{journal_header_income.id}"
    )

    assert response.status_code == 200
    data = response.json()
    assert "id" in data
    assert data["message"] == "Journal line created successfully"

    journal_line = JournalLine.get(data["id"])
    assert journal_line is not None
    assert journal_line.line_type == OperationTypeEnum.DEBIT
    assert journal_line.amount == 0.0
    assert journal_line.journal_header_id == journal_header_income.id


def test_create_multiple_lines_for_header(
    test_auth_client, db_test, journal_header_income
):
    repository = JournalRepository()
    header = repository.get_journal_header(journal_header_income.id)

    response1 = test_auth_client.post(
        f"/transactions/journal-line/{header.id}",
        json={"line_type": "DEBIT", "amount": 100.0, "account_id": "ACC_DEBIT"},
    )
    assert response1.status_code == 200
    data_debit = response1.json()
    debit = JournalLine.get(data_debit["id"])
    assert debit is not None
    assert data_debit["message"] == "Journal line created successfully"

    response2 = test_auth_client.post(f"/transactions/journal-line/{header.id}")
    assert response2.status_code == 200
    data_credit = response1.json()
    credit = JournalLine.get(data_credit["id"])
    assert credit is not None
    assert data_credit["message"] == "Journal line created successfully"


def test_create_journal_line_header_not_found(test_auth_client, db_test):
    """Testa tentativa de criar linha para header inexistente"""
    invalid_id = "********-0000-0000-0000-********0000"
    response = test_auth_client.post(
        f"/transactions/journal-line/{invalid_id}",
        json={"line_type": "DEBIT", "amount": 100.0},
    )

    assert response.status_code == 404
    assert "Journal Header ID not found" in response.json()["detail"]


def test_create_journal_line_database_error(
    test_auth_client, db_test, journal_header_income, monkeypatch
):
    """Testa erro durante a persistência no banco"""
    original_save = JournalLine.save

    def mock_save_fail(self):
        raise SQLAlchemyError("Simulated database error")

    monkeypatch.setattr(JournalLine, "save", mock_save_fail)

    response = test_auth_client.post(
        f"/transactions/journal-line/{journal_header_income.id}",
        json={"line_type": "DEBIT", "amount": 100.0},
    )

    monkeypatch.setattr(JournalLine, "save", original_save)

    assert response.status_code == 500
    assert "Failed to insert Journal Line" in response.json()["detail"]
    assert "Simulated database error" in response.json()["detail"]


def test_create_journal_line_invalid_header_status(test_auth_client, db_test):
    """Testa criação em header com status inválido"""
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.CANCELLED,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        reconciliation_id=reconciliation.id,
        status=JournalHeaderStatusEnum.NEW,
    )
    journal_header.save()

    response = test_auth_client.post(f"/transactions/journal-line/{journal_header.id}")

    assert response.status_code == 400
    assert (
        "Error: Cannot create journal lines. Associated reconciliation must be ACTIVE. "
        "Current status: CANCELLED" in response.json()["detail"]
    )


@pytest.fixture
def active_reconciliation(db_test):
    reconciliation = Reconciliation(
        competence="2024-03",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="123",
        operator="test_operator",
    ).save()
    return reconciliation


@pytest.fixture
def journal_line_with_invoice(active_reconciliation):
    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        description="Test Journal Header",
        notes="Test notes",
        status="NEW",
        reconciliation_id=active_reconciliation.id,
        source_reference_id="123",
    ).save()

    journal_line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        description="Test Journal Line",
        account_id="123",
        journal_header_id=journal_header.id,
    ).save()

    linked_invoice = LinkedInvoice(
        platform_invoice_id="invoice_123",
        invoice_number="NF-123",
        file_path="/path/to/invoice.pdf",
        journal_line_id=journal_line.id,
        link_date=datetime.now(),
    ).save()

    return journal_header, journal_line, linked_invoice


def test_patch_journal_line_with_new_invoice(
    test_auth_client, journal_line_with_invoice
):
    """Test updating a journal line by adding a new invoice"""
    journal_header, journal_line, old_invoice = journal_line_with_invoice

    # Garante que o journal_line está salvo e com ID válido
    journal_line._metadata = {
        "has_linked_invoices": False,
        "is_linked_invoice_applicable": None,
        "invoice_motive": None,
    }
    journal_line.save()
    journal_line_id = str(journal_line.id)

    # Define os dados da nova fatura
    new_invoice_number = "INV-001"
    response = test_auth_client.patch(
        f"/transactions/journal-line/{journal_line_id}",
        json={
            "amount": 150.00,
            "description": "Updated description",
            "should_link_invoices": True,
            "linked_invoices": [
                {
                    "platform_invoice_id": "invoice_2",
                    "link_date": datetime.now().isoformat(),
                    "link_percentual": 100.0,
                    "invoice_number": new_invoice_number,
                    "file_path": "/path/to/file",
                },
                {
                    "platform_invoice_id": "invoice_3",
                    "link_date": datetime.now().isoformat(),
                },
            ],
        },
    )

    assert response.status_code == 200
    response_data = response.json()

    linked_invoice = response_data["linked_invoices"][0]
    has_linked_invoices = response_data["has_linked_invoices"]

    assert linked_invoice["invoice_number"] == "INV-001"
    assert has_linked_invoices is None

    # # Verifica se as alterações foram salvas no banco
    with session_scope():
        new_invoices = LinkedInvoice().filter_by(
            journal_line_id=journal_line_id, deleted=False
        )
        new_invoices.sort(key=lambda invoice: invoice.platform_invoice_id)

        assert len(new_invoices) == 2
        assert new_invoices[0].platform_invoice_id == "invoice_2"
        assert new_invoices[0].invoice_number == "INV-001"
        assert new_invoices[1].platform_invoice_id == "invoice_3"


def test_patch_journal_line_remove_invoices(
    test_auth_client, journal_line_with_invoice
):
    """Test updating a journal line by removing all invoices"""
    journal_header, journal_line, old_invoice = journal_line_with_invoice

    # Garante que o journal_line está salvo e com ID válido
    journal_line.save()
    journal_line_id = str(journal_line.id)
    old_invoice_id = str(old_invoice.id)

    response = test_auth_client.patch(
        f"/transactions/journal-line/{journal_line_id}",
        json={
            "should_link_invoices": True,
            "linked_invoices": [],
        },
    )

    assert response.status_code == 200

    # Busca os registros atualizados usando uma nova sessão
    with session_scope() as session:
        # Verifica se a invoice antiga foi deletada
        old_invoice_updated = session.get(LinkedInvoice, old_invoice_id)
        assert old_invoice_updated.deleted is True

        # Verifica que não existem invoices ativas
        active_invoices = LinkedInvoice().filter_by(
            journal_line_id=journal_line_id, deleted=False
        )
        assert len(active_invoices) == 0


def test_patch_journal_line_with_invoice_metadata(
    test_auth_client, journal_line_with_invoice
):
    """Test updating a journal line with invoice applicability metadata"""
    journal_header, journal_line, _ = journal_line_with_invoice

    # Garante que o journal_line está salvo e com ID válido
    journal_line.save()
    journal_line_id = str(journal_line.id)

    response = test_auth_client.patch(
        f"/transactions/journal-line/{journal_line_id}",
        json={
            "is_linked_invoice_applicable": False,
            "invoice_motive": "Pagamento de Tributos",
        },
    )

    assert response.status_code == 200

    # Busca o journal_line atualizado usando uma nova sessão
    with session_scope() as session:
        updated_line = session.get(JournalLine, journal_line_id)
        assert updated_line is not None
        assert updated_line._metadata.get("is_linked_invoice_applicable") is False
        assert updated_line._metadata.get("invoice_motive") == "pagamento_de_tributos"


def test_patch_journal_line_with_other_invoice_motive(
    test_auth_client, journal_line_with_invoice
):
    """Test updating a journal line with 'Outros' invoice motive"""
    journal_header, journal_line, _ = journal_line_with_invoice

    # Garante que o journal_line está salvo e com ID válido
    journal_line.has_linked_invoices = True
    journal_line._metadata = {
        "has_linked_invoices": False,
        "is_linked_invoice_applicable": None,
        "invoice_motive": None,
    }
    journal_line.save()
    journal_line_id = str(journal_line.id)

    response = test_auth_client.patch(
        f"/transactions/journal-line/{journal_line_id}",
        json={
            "has_linked_invoices": None,
            "is_linked_invoice_applicable": False,
            "linked_invoices": [],
            "should_link_invoices": True,
            "invoice_motive": "outros",
        },
    )

    assert response.status_code == 200

    # Verifica se o campo is_linked_invoice_applicable está presente na response
    response_data = response.json()
    assert response_data["has_linked_invoices"] is None
    assert response_data["is_linked_invoice_applicable"] is False

    # Busca o journal_line atualizado usando uma nova sessão
    with session_scope() as session:
        updated_line = session.get(JournalLine, journal_line_id)
        assert updated_line is not None
        assert updated_line._metadata.get("is_linked_invoice_applicable") is False
        assert updated_line._metadata.get("invoice_motive") == "outros"


def test_patch_journal_line_with_custom_invoice_motive(
    test_auth_client, journal_line_with_invoice
):
    """Test updating a journal line with a custom invoice motive"""
    journal_header, journal_line, _ = journal_line_with_invoice

    # Garante que o journal_line está salvo e com ID válido
    journal_line.save()
    journal_line_id = str(journal_line.id)

    custom_motive = "Motivo Personalizado"
    response = test_auth_client.patch(
        f"/transactions/journal-line/{journal_line_id}",
        json={
            "is_linked_invoice_applicable": False,
            "invoice_motive": custom_motive,
        },
    )

    assert response.status_code == 200

    # Busca o journal_line atualizado usando uma nova sessão
    with session_scope() as session:
        updated_line = session.get(JournalLine, journal_line_id)
        assert updated_line is not None
        assert updated_line._metadata.get("is_linked_invoice_applicable") is False
        assert updated_line._metadata.get("invoice_motive") == custom_motive


def test_patch_journal_header_not_found(test_auth_client, db_test):
    """Test journal header not found error"""
    invalid_id = "********-0000-0000-0000-********0000"

    response = test_auth_client.patch(
        f"/transactions/journal-header/{invalid_id}",
        json={
            "description": "New Description",
            "notes": "Updated notes",
        },
    )

    assert response.status_code == 404
    assert (
        response.json()["detail"] == f"Error: Journal Header ID not found {invalid_id}"
    )


def test_patch_journal_header_not_found_error(test_auth_client, db_test):
    invalid_id = "ffffffff-ffff-ffff-ffff-ffffffffffff"
    response = test_auth_client.patch(
        f"/transactions/journal-header/{invalid_id}",
        json={"description": "Should not update"},
    )
    assert response.status_code == 404
    assert (
        f"Error: Journal Header ID not found {invalid_id}" in response.json()["detail"]
    )


def test_patch_journal_header_successful(test_auth_client, journal_header_expense):
    """Test successful journal header update"""
    header_id = str(journal_header_expense.id)
    new_description = "Updated Description"
    new_notes = "Updated notes"

    response = test_auth_client.patch(
        f"/transactions/journal-header/{header_id}",
        json={
            "description": new_description,
            "notes": new_notes,
        },
    )

    assert response.status_code == 200
    response_data = response.json()
    assert response_data["description"] == new_description

    # Verificar se as alterações foram salvas no banco
    with session_scope() as session:
        updated_header = session.get(JournalHeader, header_id)
        assert updated_header.description == new_description
        assert updated_header.notes == new_notes


def test_edit_journal_header_invalid_reconciliation_status(test_auth_client, db_test):
    """Tests editing a journal header when reconciliation is not ACTIVE"""
    # Create a reconciliation with CANCELLED status
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.CANCELLED,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    # Create a journal header associated with the cancelled reconciliation
    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        reconciliation_id=reconciliation.id,
        description="Test Header",
        status=TransactionStatusEnum.NEW,
    )
    journal_header.save()

    # Try to update the journal header
    response = test_auth_client.patch(
        f"/transactions/journal-header/{journal_header.id}",
        json={"description": "Updated Description"},
    )

    # Assert the response
    assert response.status_code == 400
    assert (
        "Error: Cannot update journal lines. Associated reconciliation must be ACTIVE. "
        "Current status: CANCELLED" in response.json()["detail"]
    )


def test_patch_journal_header_cpf_and_name_creates_new_partner_with_new_name(
    test_auth_client, db_test, journal_header_expense
):
    """
    If a CPF/CNPJ that already exists is sent along with a new name,
    a new PartnerEntity must be created with the provided name,
    ignoring the name of the existing partner in the database.
    """
    existing_partner = PartnerEntity(cpf_cnpj="55566677788", name="Nome Antigo")
    existing_partner.save()

    journal_header_expense.partner_entity_id = None
    journal_header_expense.save()

    response = test_auth_client.patch(
        f"/transactions/journal-header/{journal_header_expense.id}",
        json={
            "partner_entity_cpf_cnpj": "55566677788",
        },
    )

    assert response.status_code == 200
    result = response.json()
    assert result.get("trading_entity") is not None
    assert result["trading_entity"]["cpf_cnpj"] == "55566677788"

    # Deve existir dois partners com o mesmo CPF/CNPJ, mas nomes diferentes
    partners = PartnerEntity().filter_by(cpf_cnpj="55566677788")
    assert len(partners) == 2
    nomes = {p.name for p in partners}
    assert "Nome Antigo" in nomes

    # O novo partner deve estar vinculado ao header
    assert journal_header_expense.partner_entity_id is not None
    assert journal_header_expense.partner_entity_id != existing_partner.id
    assert journal_header_expense.partner_entity.cpf_cnpj == "55566677788"


def test_patch_journal_header_empty_cpf_does_not_update_partner(
    test_auth_client, db_test, journal_header_expense
):
    """
    If CPF/CNPJ is sent as empty strings,
    no PartnerEntity should be created or updated,
    and the header should remain without a partner or keep the previous one.
    """
    # Garante que o header não tem partner vinculado
    journal_header_expense.partner_entity_id = None
    journal_header_expense.save()

    response = test_auth_client.patch(
        f"/transactions/journal-header/{journal_header_expense.id}",
        json={"partner_entity_cpf_cnpj": ""},
    )

    assert response.status_code == 200
    result = response.json()
    # O trading_entity não deve ser retornado ou deve ser None
    assert not result.get("trading_entity")
    # O header deve continuar sem partner_entity vinculado
    assert journal_header_expense.partner_entity_id is None


def test_patch_journal_header_empty_name_updates_partner(
    test_auth_client, db_test, journal_header_expense
):
    """
    If no name is sent as empty strings,
    the PartnerEntity should be updated.
    """
    cpf_cnpj = "55566677788"

    existing_partner = PartnerEntity(cpf_cnpj=cpf_cnpj, name="Old Partner")
    existing_partner.save()

    journal_header_expense.partner_entity_id = existing_partner.id
    journal_header_expense.save()

    response = test_auth_client.patch(
        f"/transactions/journal-header/{journal_header_expense.id}",
        json={"partner_entity_name": ""},
    )

    assert response.status_code == 200
    result = response.json()
    trading_entity = result.get("trading_entity")
    assert trading_entity.get("name") is None
    partner = PartnerEntity().filter_by(cpf_cnpj=cpf_cnpj)[0]
    assert partner.name is None


def test_journal_status_new_when_lines_missing_accounts(test_auth_client, db_test):
    """
    Should keep status NEW if any journal line is missing account_id.
    """
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        cockpit_account_id="123",
        description="Header missing account",
        notes="Test notes",
        status=TransactionStatusEnum.NEW,
        reconciliation_id=reconciliation.id,
        source_reference_id="456",
    )
    journal_header.save()

    debit_line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        account_id=None,  # missing account
        journal_header_id=journal_header.id,
    )
    debit_line.save()

    credit_line = JournalLine(
        line_type=OperationTypeEnum.CREDIT,
        amount=100.00,
        account_id="Account 1",
        journal_header_id=journal_header.id,
    )
    credit_line.save()

    response = test_auth_client.post(f"/transactions/{journal_header.id}/status")
    assert response.status_code == 200
    assert response.json()["status"] == TransactionStatusEnum.NEW


def test_journal_status_new_when_debits_and_credits_do_not_match(
    test_auth_client, db_test
):
    """
    Should keep status NEW if debit and credit sums do not match.
    """
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        cockpit_account_id="123",
        description="Header unbalanced",
        notes="Test notes",
        status=TransactionStatusEnum.NEW,
        reconciliation_id=reconciliation.id,
        source_reference_id="456",
    )
    journal_header.save()

    debit_line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=120.00,
        account_id="Account 2",
        journal_header_id=journal_header.id,
    )
    debit_line.save()

    credit_line = JournalLine(
        line_type=OperationTypeEnum.CREDIT,
        amount=100.00,
        account_id="Account 1",
        journal_header_id=journal_header.id,
    )
    credit_line.save()

    response = test_auth_client.post(f"/transactions/{journal_header.id}/status")
    assert response.status_code == 200
    assert response.json()["status"] == TransactionStatusEnum.NEW


def test_journal_status_reconciled_when_all_conditions_met(test_auth_client, db_test):
    """
    Should set status RECONCILED if all lines have accounts and debits match credits.
    """
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    journal_header = JournalHeader(
        amount=100.00,
        date=datetime.now(),
        cockpit_account_id="123",
        description="Header reconciled",
        notes="Test notes",
        status=TransactionStatusEnum.NEW,
        reconciliation_id=reconciliation.id,
        source_reference_id="456",
    )
    journal_header.save()

    debit_line = JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        account_id="Account 2",
        journal_header_id=journal_header.id,
    )
    debit_line.save()

    credit_line = JournalLine(
        line_type=OperationTypeEnum.CREDIT,
        amount=100.00,
        account_id="Account 1",
        journal_header_id=journal_header.id,
    )
    credit_line.save()

    response = test_auth_client.post(f"/transactions/{journal_header.id}/status")
    assert response.status_code == 200
    assert response.json()["status"] == TransactionStatusEnum.RECONCILED


def test_patch_journal_header_unexpected_error(
    monkeypatch, test_auth_client, journal_header_expense
):
    def mock_save_fail(*args, **kwargs):
        raise Exception("Simulated error")

    monkeypatch.setattr(journal_header_expense, "save", mock_save_fail)
    response = test_auth_client.patch(
        f"/transactions/journal-header/{journal_header_expense.id}",
        json={"description": "Should not update"},
    )
    assert response.status_code == 400
    assert "Error updating journal header" in response.json()["detail"]


def test_patch_journal_header_update_partner_cpf_cnpj(
    test_auth_client, db_test, journal_header_expense
):
    """Should update the partner's cpf_cnpj if it is different"""
    partner = PartnerEntity(cpf_cnpj="11111111111", name="Original Name")
    partner.save()
    journal_header_expense.partner_entity_id = partner.id
    journal_header_expense.save()

    new_cpf = "***********"
    response = test_auth_client.patch(
        f"/transactions/journal-header/{journal_header_expense.id}",
        json={"partner_entity_cpf_cnpj": new_cpf},
    )
    assert response.status_code == 200
    updated_partner = PartnerEntity().filter_by(id=partner.id)[0]
    assert updated_partner.cpf_cnpj == new_cpf


def test_bulk_update_journal_line_accounts_not_found(test_auth_client, db_test):
    # Crie um ID inexistente
    invalid_id = "ffffffff-ffff-ffff-ffff-ffffffffffff"
    response = test_auth_client.patch(
        "/transactions/journal-lines/accounts",
        json={
            "journal_line_ids": [invalid_id],
            "updated_data": {"account_id": "bulk_account_id_updated"},
        },
    )
    assert response.status_code == 400
    assert "Journal lines not found" in response.json()["detail"]


def test_patch_journal_header_only_name_creates_partner_with_only_name(
    test_auth_client, db_test, journal_header_expense
):
    """
    If only a name is sent (no cpf_cnpj), a new PartnerEntity must be created
    with only the name and linked to the header.
    """
    journal_header_expense.partner_entity_id = None
    journal_header_expense.save()

    new_name = "Parceiro Só Nome"
    response = test_auth_client.patch(
        f"/transactions/journal-header/{journal_header_expense.id}",
        json={"partner_entity_name": new_name},
    )

    assert response.status_code == 200
    result = response.json()
    assert result.get("trading_entity") is not None
    assert result["trading_entity"]["name"] == new_name
    assert result["trading_entity"]["cpf_cnpj"] is None

    partners = [p for p in PartnerEntity().filter_by(name=new_name)]
    assert len(partners) == 1
    assert partners[0].cpf_cnpj is None
    assert journal_header_expense.partner_entity_id == partners[0].id


@pytest.fixture
def mock_parallel_processing():
    """Fixture for reusing parallel processing mocks"""
    call_times = {}
    thread_ids = set()

    def mock_get_accounts_by_guids(bank_account_guids):
        return [
            {"id": "account_1", "bank_account_guid": "guid_1"},
            {"id": "account_2", "bank_account_guid": "guid_2"},
            {"id": "account_3", "bank_account_guid": "guid_3"},
        ]

    def mock_get_transactions(account_id, from_date, to_date):
        thread_id = threading.current_thread().ident
        thread_ids.add(thread_id)
        time.sleep(0.1)
        call_times[account_id] = {"thread_id": thread_id}

        return [
            {
                "id": str(uuid4()),
                "transaction_type": "CREDIT",
                "amount": 100.0,
                "date": f"2024-10-{i+1:02d}T10:00:00Z",
                "description": f"Transaction {i}",
            }
            for i in range(10)
        ]

    return {
        "call_times": call_times,
        "thread_ids": thread_ids,
        "mock_get_accounts_by_guids": mock_get_accounts_by_guids,
        "mock_get_transactions": mock_get_transactions,
    }


def test_parallel_account_processing_with_multiple_accounts(
    test_auth_client, db_test, mock_parallel_processing
):
    """Test parallel processing with multiple accounts using ThreadPoolExecutor"""
    reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="test_customer",
        operator="Test Operator",
    )
    reconciliation.save()

    with patch(
        "infrastructure.transactions_api_client.TransactionsApiClient."
        "get_accounts_by_guids"
    ) as mock_get_accounts:
        with patch(
            "infrastructure.transactions_api_client.TransactionsApiClient."
            "get_transactions"
        ) as mock_get_trans:

            mock_get_accounts.side_effect = mock_parallel_processing[
                "mock_get_accounts_by_guids"
            ]
            mock_get_trans.side_effect = mock_parallel_processing[
                "mock_get_transactions"
            ]

            response = test_auth_client.post(
                "/reconciliation/draft",
                json={
                    "cockpit_customer_id": "test_customer",
                    "competence": "2024-10",
                    "bank_account_guids": ["guid_1", "guid_2", "guid_3"],
                },
            )

    assert response.status_code == 200
    assert len(mock_parallel_processing["call_times"]) == 3
    assert len(mock_parallel_processing["thread_ids"]) > 1

    with session_scope() as session:
        journal_headers = (
            session.query(JournalHeader)
            .filter_by(reconciliation_id=reconciliation.id)
            .all()
        )
        assert len(journal_headers) == 30


def test_parallel_processing_with_error_isolation(
    test_auth_client, db_test, mock_parallel_processing
):
    """Test error isolation when one account fails during parallel processing"""

    def mock_get_accounts_with_error(bank_account_guids):
        return [
            {"id": "account_1", "bank_account_guid": "guid_1"},
            {"id": "account_error", "bank_account_guid": "guid_2"},
            {"id": "account_3", "bank_account_guid": "guid_3"},
        ]

    def mock_get_transactions_with_error(account_id, from_date, to_date):
        thread_id = threading.current_thread().ident
        mock_parallel_processing["thread_ids"].add(thread_id)
        time.sleep(0.1)
        mock_parallel_processing["call_times"][account_id] = {"thread_id": thread_id}

        if account_id == "account_error":
            raise Exception("API error for this account")

        return mock_parallel_processing["mock_get_transactions"](
            account_id, from_date, to_date
        )

    reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="test_customer",
        operator="Test Operator",
    )
    reconciliation.save()

    with patch(
        "infrastructure.transactions_api_client.TransactionsApiClient."
        "get_accounts_by_guids"
    ) as mock_get_accounts:
        with patch(
            "infrastructure.transactions_api_client.TransactionsApiClient."
            "get_transactions"
        ) as mock_get_trans:

            mock_get_accounts.side_effect = mock_get_accounts_with_error
            mock_get_trans.side_effect = mock_get_transactions_with_error

            test_auth_client.post(
                "/reconciliation/draft",
                json={
                    "cockpit_customer_id": "test_customer",
                    "competence": "2024-10",
                    "bank_account_guids": ["guid_1", "guid_2", "guid_3"],
                },
            )

    assert len(mock_parallel_processing["call_times"]) == 3
    assert len(mock_parallel_processing["thread_ids"]) > 1

    with session_scope() as session:
        journal_headers = (
            session.query(JournalHeader)
            .filter_by(reconciliation_id=reconciliation.id)
            .all()
        )
        assert len(journal_headers) == 20


def test_parallel_processing_with_single_account(
    test_auth_client, db_test, mock_parallel_processing
):
    """Test parallel processing with single account"""

    def mock_get_single_account(bank_account_guids):
        return [{"id": "single_account", "bank_account_guid": "guid_single"}]

    def mock_get_transactions_single(account_id, from_date, to_date):
        thread_id = threading.current_thread().ident
        mock_parallel_processing["thread_ids"].add(thread_id)
        time.sleep(0.1)
        mock_parallel_processing["call_times"][account_id] = {"thread_id": thread_id}

        return [
            {
                "id": str(uuid4()),
                "transaction_type": "CREDIT",
                "amount": 100.0,
                "date": f"2024-10-{i+1:02d}T10:00:00Z",
                "description": f"Transaction {i}",
            }
            for i in range(5)
        ]

    reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="test_customer",
        operator="Test Operator",
    )
    reconciliation.save()

    with patch(
        "infrastructure.transactions_api_client.TransactionsApiClient."
        "get_accounts_by_guids"
    ) as mock_get_accounts:
        with patch(
            "infrastructure.transactions_api_client.TransactionsApiClient."
            "get_transactions"
        ) as mock_get_trans:

            mock_get_accounts.side_effect = mock_get_single_account
            mock_get_trans.side_effect = mock_get_transactions_single

            response = test_auth_client.post(
                "/reconciliation/draft",
                json={
                    "cockpit_customer_id": "test_customer",
                    "competence": "2024-10",
                    "bank_account_guids": ["guid_single"],
                },
            )

    assert response.status_code == 200
    assert len(mock_parallel_processing["call_times"]) == 1
    assert "single_account" in mock_parallel_processing["call_times"]

    with session_scope() as session:
        journal_headers = (
            session.query(JournalHeader)
            .filter_by(reconciliation_id=reconciliation.id)
            .all()
        )
        assert len(journal_headers) == 5


def test_parallel_processing_with_duplicate_accounts(
    test_auth_client, db_test, mock_parallel_processing
):
    """Test deduplication when API returns duplicate accounts"""

    def mock_get_accounts_with_duplicates(bank_account_guids):
        return [
            {"id": "account_1", "bank_account_guid": "guid_1"},
            {"id": "account_2", "bank_account_guid": "guid_2"},
            {"id": "account_1", "bank_account_guid": "guid_1"},  # Duplicate
            {"id": "account_3", "bank_account_guid": "guid_3"},
            {"id": "account_2", "bank_account_guid": "guid_2"},  # Duplicate
        ]

    def mock_get_transactions_track_calls(account_id, from_date, to_date):
        thread_id = threading.current_thread().ident
        mock_parallel_processing["thread_ids"].add(thread_id)
        time.sleep(0.1)
        mock_parallel_processing["call_times"][account_id] = {"thread_id": thread_id}

        return [
            {
                "id": str(uuid4()),
                "transaction_type": "CREDIT",
                "amount": 100.0,
                "date": f"2024-10-{i+1:02d}T10:00:00Z",
                "description": f"Transaction {i}",
            }
            for i in range(10)
        ]

    reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="test_customer",
        operator="Test Operator",
    )
    reconciliation.save()

    with patch(
        "infrastructure.transactions_api_client.TransactionsApiClient."
        "get_accounts_by_guids"
    ) as mock_get_accounts:
        with patch(
            "infrastructure.transactions_api_client.TransactionsApiClient."
            "get_transactions"
        ) as mock_get_trans:

            mock_get_accounts.side_effect = mock_get_accounts_with_duplicates
            mock_get_trans.side_effect = mock_get_transactions_track_calls

            response = test_auth_client.post(
                "/reconciliation/draft",
                json={
                    "cockpit_customer_id": "test_customer",
                    "competence": "2024-10",
                    "bank_account_guids": ["guid_1", "guid_2", "guid_3"],
                },
            )

    assert response.status_code == 200
    assert len(mock_parallel_processing["call_times"]) == 3  # Only 3 unique calls
    assert "account_1" in mock_parallel_processing["call_times"]
    assert "account_2" in mock_parallel_processing["call_times"]
    assert "account_3" in mock_parallel_processing["call_times"]

    with session_scope() as session:
        journal_headers = (
            session.query(JournalHeader)
            .filter_by(reconciliation_id=reconciliation.id)
            .all()
        )
        assert len(journal_headers) == 30  # 3 unique accounts x 10 transactions
