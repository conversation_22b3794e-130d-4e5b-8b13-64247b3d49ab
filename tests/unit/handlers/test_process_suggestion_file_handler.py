import json
from decimal import Decimal
from typing import List
from unittest.mock import MagicMock, patch

import pytest

from domain.models import SuggestedInvoice, SuggestionSource, SuggestionTypeEnum
from index import process_suggestion_file_handler


@pytest.fixture
def mock_invoices_api_client():
    with patch("index.InvoicesApiClient") as mock:
        yield mock


@pytest.fixture
def mock_match_repository():
    with patch("index.MatchRepository") as mock:
        yield mock


@pytest.fixture
def mock_suggestion_queue():
    with patch("index.SuggestionQueue") as mock:
        yield mock


binder_suggestion = {
    "suggestion_id": "123456",
    "cockpit_account_id": "acct-789012",
    "platform_transaction_id": "txn-345678",
    "platform_invoice_id": None,
    "should_not_be_matched": None,
    "ranking": 0.95,
    "exact_match": True,
    "version": 2,
    "suggested_account": "12345",
    "categorization_rule": {"kind": "regex", "name": "tarifas_bancarias"},
    "suggestion_type": "ACCOUNT",
}


@patch("index.logger")
def test_process_match_file_handler_valid_event(
    mock_logger,
    mock_match_repository,
    mock_suggestion_queue,
    mock_invoices_api_client,
    db_test,
):
    event = {"Records": [{"body": json.dumps(binder_suggestion)}]}
    mock_import_instance = MagicMock()
    mock_import_instance.import_match.return_value = [
        SuggestedInvoice(
            suggestion_id="sug-123456",
            cockpit_account_id="acct-789012",
            platform_transaction_id="txn-345678",
            platform_invoice_id="inv-901234",
            ranking=0.95,
            exact_match=True,
            version=2,
            should_not_be_matched=False,
            account_data={
                "number": "12345-6",
                "rule_type": "category",
                "rule_name": "Office Supplies",
            },
            suggestion_type="category",
        )
    ]
    mock_suggestion_queue.return_value = mock_import_instance

    process_suggestion_file_handler(event, None)

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 1 imported from 1 records"
    )
    mock_import_instance.import_match.assert_called_once_with(binder_suggestion)


@patch("index.logger")
def test_process_match_file_handler_check_suggested_invoice_account(
    mock_logger, mock_invoices_api_client, db_test
):
    event = {"Records": [{"body": json.dumps(binder_suggestion)}]}

    mock_invoices_api_client.return_value.get_invoices_by_ids.return_value = []

    process_suggestion_file_handler(event, None)

    suggested_invoice: List[SuggestedInvoice] = SuggestedInvoice().all()

    assert len(suggested_invoice) == 1
    assert suggested_invoice[0].account_data == {
        "number": "12345",
        "rule_type": "regex",
        "rule_name": "tarifas_bancarias",
    }

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 1 imported from 1 records"
    )


@patch("index.logger")
def test_process_match_file_handler_invoice_suggestion(
    mock_logger,
    mock_invoices_api_client,
    db_test,
):
    invoice_suggestion = {
        "cnpj_cliente_bhub": "************",
        "suggestion_id": "789012",
        "cockpit_account_id": "acct-789012",
        "platform_transaction_id": "txn-345678",
        "platform_invoice_id": "inv-901234",
        "should_not_be_matched": None,
        "ranking": 0.95,
        "exact_match": True,
        "version": 2,
        "suggested_account": None,
        "categorization_rule": None,
        "suggestion_type": "INVOICE",
    }

    event = {"Records": [{"body": json.dumps(invoice_suggestion)}]}

    mock_invoice_data = {
        "id": "inv-901234",
        "issueDate": "2023-01-01",
        "takerName": "Fornecedor Teste",
        "issuerDocument": "**************",
        "issuerName": "Emissor Teste",
        "amount": 100.50,
        "number": "12345",
    }

    mock_invoices_api_client.return_value.get_invoices_by_ids.return_value = [
        mock_invoice_data
    ]

    process_suggestion_file_handler(event, None)

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 1 imported from 1 records"
    )
    mock_invoices_api_client.return_value.get_invoices_by_ids.assert_called_once_with(
        ["inv-901234"]
    )


@patch("index.logger")
def test_process_match_file_handler_existing_suggestion(
    mock_logger,
    mock_invoices_api_client,
    db_test,
):
    existing_suggestion = SuggestedInvoice(
        suggestion_id="old-123",
        cockpit_account_id="acct-789012",
        platform_transaction_id="txn-345678",
        platform_invoice_id=None,
        ranking=0.85,
        exact_match=False,
        version=1,
        should_not_be_matched=False,
        account_data={"number": "54321", "rule_type": "regex", "rule_name": "old_rule"},
        suggestion_type="ACCOUNT",
    )
    existing_suggestion.save()

    event = {"Records": [{"body": json.dumps(binder_suggestion)}]}

    mock_import_instance = MagicMock()
    mock_import_instance.import_match.return_value = [
        SuggestedInvoice(
            suggestion_id="123456",
            cockpit_account_id="acct-789012",
            platform_transaction_id="txn-345678",
            platform_invoice_id=None,
            ranking=0.95,
            exact_match=True,
            version=2,
            should_not_be_matched=False,
            account_data={
                "number": "12345",
                "rule_type": "regex",
                "rule_name": "tarifas_bancarias",
            },
            suggestion_type="ACCOUNT",
        )
    ]

    mock_suggestion_queue.return_value = mock_import_instance

    process_suggestion_file_handler(event, None)

    updated_suggestion = SuggestedInvoice().all()
    assert len(updated_suggestion) == 1
    assert updated_suggestion[0].suggestion_id == "123456"
    assert updated_suggestion[0].ranking == Decimal("0.95")
    assert updated_suggestion[0].exact_match is True
    assert updated_suggestion[0].version == "2"
    assert updated_suggestion[0].account_data == {
        "number": "12345",
        "rule_type": "regex",
        "rule_name": "tarifas_bancarias",
    }

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 1 imported from 1 records"
    )


@patch("index.logger")
def test_process_match_file_handler_empty_event(
    mock_logger, mock_invoices_api_client, db_test
):
    event = {"Records": []}
    process_suggestion_file_handler(event, None)
    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 0 imported from 0 records"
    )


@patch("index.logger")
def test_process_match_file_handler_invalid_event(mock_logger, db_test):
    event = {"Records": [{"body": "invalid_json"}]}

    with pytest.raises(Exception):
        process_suggestion_file_handler(event, None)


@patch("index.logger")
def test_process_match_file_handler_invoice_not_found(
    mock_logger,
    mock_invoices_api_client,
    db_test,
):
    invoice_suggestion = {
        "suggestion_id": "789012",
        "cockpit_account_id": "acct-789012",
        "platform_transaction_id": "txn-345678",
        "platform_invoice_id": "inv-not-found",
        "should_not_be_matched": None,
        "ranking": 0.95,
        "exact_match": True,
        "version": 2,
        "suggested_account": None,
        "categorization_rule": None,
        "suggestion_type": "INVOICE",
    }

    event = {"Records": [{"body": json.dumps(invoice_suggestion)}]}

    mock_invoices_api_client.return_value.get_invoices_by_ids.return_value = []

    process_suggestion_file_handler(event, None)

    suggested_invoices = SuggestedInvoice().all()
    assert len(suggested_invoices) == 1
    assert suggested_invoices[0].platform_invoice_id == "inv-not-found"
    assert suggested_invoices[0].invoice_date is None
    assert suggested_invoices[0].invoice_company_name is None

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 1 imported from 1 records"
    )


@patch("index.logger")
def test_process_match_file_handler_update_existing_invoice_suggestion(
    mock_logger,
    mock_invoices_api_client,
    db_test,
):
    existing_suggestion = SuggestedInvoice(
        suggestion_id="old-123",
        cockpit_account_id="acct-789012",
        platform_transaction_id="txn-345678",
        platform_invoice_id="inv-901234",
        ranking=0.85,
        exact_match=False,
        version=1,
        should_not_be_matched=False,
        account_data=None,
        suggestion_type=SuggestionTypeEnum.INVOICE,
    )
    existing_suggestion.save()

    invoice_suggestion = {
        "cnpj_cliente_bhub": "**************",
        "suggestion_id": "new-123",
        "cockpit_account_id": "acct-789012",
        "platform_transaction_id": "txn-345678",
        "platform_invoice_id": "inv-901234",
        "should_not_be_matched": None,
        "ranking": 0.95,
        "exact_match": True,
        "version": 2,
        "suggested_account": None,
        "categorization_rule": None,
        "suggestion_type": "INVOICE",
    }

    event = {"Records": [{"body": json.dumps(invoice_suggestion)}]}

    mock_invoice_data = {
        "id": "inv-901234",
        "issueDate": "2023-01-01",
        "takerName": "Fornecedor Atualizado",
        "issuerDocument": "**************",
        "issuerName": "Emissor Atualizado",
        "amount": 200.75,
        "number": "54321",
    }

    mock_invoices_api_client.return_value.get_invoices_by_ids.return_value = [
        mock_invoice_data
    ]

    process_suggestion_file_handler(event, None)

    updated_suggestions = SuggestedInvoice().all()
    assert len(updated_suggestions) == 1
    assert updated_suggestions[0].suggestion_id == "new-123"
    assert updated_suggestions[0].invoice_company_name == "Fornecedor Atualizado"
    assert updated_suggestions[0].invoice_value == "200.75"

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 1 imported from 1 records"
    )


@patch("index.logger")
def test_process_match_file_handler_info_suggestion_type(
    mock_logger,
    mock_invoices_api_client,
    db_test,
):
    info_suggestion = {
        "suggestion_id": "info-123",
        "cockpit_account_id": "acct-789012",
        "platform_transaction_id": "txn-345678",
        "platform_invoice_id": None,
        "should_not_be_matched": None,
        "ranking": 0.95,
        "exact_match": True,
        "version": 2,
        "suggested_account": None,
        "categorization_rule": None,
        "suggestion_type": "INFO",
    }

    event = {"Records": [{"body": json.dumps(info_suggestion)}]}

    process_suggestion_file_handler(event, None)

    suggested_invoices = SuggestedInvoice().all()
    assert len(suggested_invoices) == 1
    assert suggested_invoices[0].suggestion_type == SuggestionTypeEnum.INFO
    assert suggested_invoices[0].account_data == {
        "number": None,
        "rule_name": None,
        "rule_type": None,
    }
    assert suggested_invoices[0].source == SuggestionSource.BINDER

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 1 imported from 1 records"
    )


@patch("index.logger")
def test_process_match_file_handler_combined_invoice_and_account_suggestion(
    mock_logger,
    mock_invoices_api_client,
    db_test,
):
    combined_suggestion = {
        "cnpj_cliente_bhub": "************",
        "suggestion_id": "comb-123",
        "cockpit_account_id": "acct-789012",
        "platform_transaction_id": "txn-345678",
        "platform_invoice_id": "inv-901234",
        "should_not_be_matched": None,
        "ranking": 0.95,
        "exact_match": True,
        "version": 2,
        "suggested_account": "65432",
        "categorization_rule": {"kind": "category", "name": "combined_test"},
        "suggestion_type": "COMBINED",
    }

    event = {"Records": [{"body": json.dumps(combined_suggestion)}]}

    mock_invoice_data = {
        "id": "inv-901234",
        "issueDate": "2023-01-01",
        "takerName": "Fornecedor Combinado",
        "issuerDocument": "************",
        "issuerName": "Emissor Combinado",
        "amount": 300.50,
        "number": "67890",
    }

    mock_invoices_api_client.return_value.get_invoices_by_ids.return_value = [
        mock_invoice_data
    ]

    process_suggestion_file_handler(event, None)

    suggested_invoices = SuggestedInvoice().all()
    assert len(suggested_invoices) == 2

    invoice_suggestion = next(
        (
            si
            for si in suggested_invoices
            if si.suggestion_type == SuggestionTypeEnum.INVOICE
        ),
        None,
    )
    assert invoice_suggestion is not None
    assert invoice_suggestion.platform_invoice_id == "inv-901234"
    assert invoice_suggestion.invoice_company_name == "Fornecedor Combinado"
    assert invoice_suggestion.account_data == {
        "number": None,
        "rule_name": None,
        "rule_type": None,
    }

    account_suggestion = next(
        (
            si
            for si in suggested_invoices
            if si.suggestion_type == SuggestionTypeEnum.ACCOUNT
        ),
        None,
    )
    assert account_suggestion is not None
    assert account_suggestion.platform_invoice_id is None
    assert account_suggestion.account_data == {
        "number": "65432",
        "rule_type": "category",
        "rule_name": "combined_test",
    }

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 2 imported from 1 records"
    )


@patch("index.logger")
def test_process_match_file_handler_update_combined_suggestions(
    mock_logger,
    mock_invoices_api_client,
    db_test,
):
    existing_invoice = SuggestedInvoice(
        suggestion_id="old-inv-123",
        cockpit_account_id="acct-789012",
        platform_transaction_id="txn-345678",
        platform_invoice_id="inv-901234",
        ranking=0.85,
        exact_match=False,
        version=1,
        should_not_be_matched=False,
        account_data=None,
        suggestion_type=SuggestionTypeEnum.INVOICE,
    )
    existing_invoice.save()

    existing_account = SuggestedInvoice(
        suggestion_id="old-acc-123",
        cockpit_account_id="acct-789012",
        platform_transaction_id="txn-345678",
        platform_invoice_id=None,
        ranking=0.85,
        exact_match=False,
        version=1,
        should_not_be_matched=False,
        account_data={
            "number": "old-acc",
            "rule_type": "old_type",
            "rule_name": "old_name",
        },
        suggestion_type=SuggestionTypeEnum.ACCOUNT,
    )
    existing_account.save()

    combined_suggestion = {
        "cnpj_cliente_bhub": "************",
        "suggestion_id": "new-comb-123",
        "cockpit_account_id": "acct-789012",
        "platform_transaction_id": "txn-345678",
        "platform_invoice_id": "inv-901234",
        "should_not_be_matched": None,
        "ranking": 0.95,
        "exact_match": True,
        "version": 2,
        "suggested_account": "new-acc",
        "categorization_rule": {"kind": "new_type", "name": "new_name"},
        "suggestion_type": "COMBINED",
    }

    event = {"Records": [{"body": json.dumps(combined_suggestion)}]}

    mock_invoice_data = {
        "id": "inv-901234",
        "issueDate": "2023-02-01",
        "takerName": "Fornecedor Atualizado",
        "issuerDocument": "************",
        "issuerName": "Emissor Atualizado",
        "amount": 400.75,
        "number": "98765",
    }

    mock_invoices_api_client.return_value.get_invoices_by_ids.return_value = [
        mock_invoice_data
    ]

    process_suggestion_file_handler(event, None)

    updated_suggestions = SuggestedInvoice().all()
    assert len(updated_suggestions) == 2

    updated_invoice = next(
        (
            si
            for si in updated_suggestions
            if si.suggestion_type == SuggestionTypeEnum.INVOICE
        ),
        None,
    )
    assert updated_invoice.suggestion_id == "new-comb-123"
    assert updated_invoice.invoice_company_name == "Fornecedor Atualizado"
    assert updated_invoice.invoice_value == "400.75"

    updated_account = next(
        (
            si
            for si in updated_suggestions
            if si.suggestion_type == SuggestionTypeEnum.ACCOUNT
        ),
        None,
    )
    assert updated_account.suggestion_id == "new-comb-123"
    assert updated_account.account_data == {
        "number": "new-acc",
        "rule_type": "new_type",
        "rule_name": "new_name",
    }

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 2 imported from 1 records"
    )


@patch("index.logger")
def test_process_match_file_handler_combined_suggestion_without_invoice(
    mock_logger,
    mock_invoices_api_client,
    db_test,
):
    combined_suggestion = {
        "suggestion_id": "no-inv-123",
        "cockpit_account_id": "acct-789012",
        "platform_transaction_id": "txn-345678",
        "platform_invoice_id": None,
        "should_not_be_matched": None,
        "ranking": 0.95,
        "exact_match": True,
        "version": 2,
        "suggested_account": "65432",
        "categorization_rule": {"kind": "category", "name": "no_invoice_test"},
        "suggestion_type": "COMBINED",
    }

    event = {"Records": [{"body": json.dumps(combined_suggestion)}]}

    process_suggestion_file_handler(event, None)

    suggested_invoices = SuggestedInvoice().all()
    assert len(suggested_invoices) == 1

    assert suggested_invoices[0].suggestion_type == SuggestionTypeEnum.ACCOUNT
    assert suggested_invoices[0].platform_invoice_id is None
    assert suggested_invoices[0].account_data == {
        "number": "65432",
        "rule_type": "category",
        "rule_name": "no_invoice_test",
    }

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 1 imported from 1 records"
    )


@patch("index.logger")
def test_process_match_file_handler_combined_suggestion_without_account(
    mock_logger,
    mock_invoices_api_client,
    db_test,
):
    combined_suggestion = {
        "cnpj_cliente_bhub": "************",
        "suggestion_id": "no-acc-123",
        "cockpit_account_id": "acct-789012",
        "platform_transaction_id": "txn-345678",
        "platform_invoice_id": "inv-901234",
        "should_not_be_matched": None,
        "ranking": 0.95,
        "exact_match": True,
        "version": 2,
        "suggested_account": None,
        "categorization_rule": None,
        "suggestion_type": "COMBINED",
    }

    event = {"Records": [{"body": json.dumps(combined_suggestion)}]}

    mock_invoice_data = {
        "id": "inv-901234",
        "issueDate": "2023-01-01",
        "takerName": "Fornecedor Sem Conta",
        "issuerDocument": "**************",
        "issuerName": "Emissor Sem Conta",
        "amount": 500.50,
        "number": "54321",
    }

    mock_invoices_api_client.return_value.get_invoices_by_ids.return_value = [
        mock_invoice_data
    ]

    process_suggestion_file_handler(event, None)

    suggested_invoices = SuggestedInvoice().all()
    assert len(suggested_invoices) == 1

    assert suggested_invoices[0].suggestion_type == SuggestionTypeEnum.INVOICE
    assert suggested_invoices[0].platform_invoice_id == "inv-901234"
    assert suggested_invoices[0].account_data == {
        "number": None,
        "rule_name": None,
        "rule_type": None,
    }

    mock_logger.info.assert_called_with(
        "Finished binder suggestion: 1 imported from 1 records"
    )
