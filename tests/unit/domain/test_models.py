import uuid
from datetime import datetime

import pytest

from domain.models import (
    Account,
    AccountTypeEnum,
    JournalHeader,
    JournalLine,
    LinkedInvoice,
    OperationTypeEnum,
    PartnerEntity,
    Reconciliation,
    ReconciliationStatusEnum,
    TransactionStatusEnum,
)


def test_create_reconciliation(db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    assert reconciliation.id is not None
    assert reconciliation.status == ReconciliationStatusEnum.ACTIVE
    assert reconciliation.competence == "2024-09"


def test_create_partner_entity(db_test):
    partner = PartnerEntity(
        cpf_cnpj="***********", name="Partner A", description="Sample Partner"
    )
    partner.save()

    assert partner.id is not None
    assert partner.name == "Partner A"


def test_create_journal_line(db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
    ).save()

    journal_header = JournalHeader(
        amount=300.00,
        date=datetime.now(),
        description="Sample Journal Header",
        notes="Test notes",
        status="NEW",
        reconciliation_id=reconciliation.id,
    ).save()

    journal_line = JournalLine(
        line_type="DEBIT",
        amount=300.00,
        description="Sample Journal Line",
        account_id=uuid.uuid4(),
        journal_header_id=journal_header.id,
    ).save()

    assert journal_line.id is not None
    assert journal_line.amount == 300.00
    assert journal_line.journal_header_id == journal_header.id


def test_journal_line_relationships(db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
    ).save()

    journal_header = JournalHeader(
        amount=400.00,
        date=datetime.now(),
        description="Another Journal Header",
        notes="More notes",
        status="RECONCILED",
        reconciliation_id=reconciliation.id,
    ).save()

    linked_invoice = LinkedInvoice(
        platform_invoice_id="INV789",
        link_date=datetime.now(),
        link_percentual=75.0,
        invoice_number="INV003",
        file_path="/path/to/yet_another_invoice.pdf",
    ).save()

    journal_line = JournalLine(
        line_type="CREDIT",
        amount=400.00,
        description="Another Journal Line",
        account_id=uuid.uuid4(),
        journal_header_id=journal_header.id,
    ).save()

    journal_line.linked_invoices.append(linked_invoice)
    journal_line.save()

    assert journal_line.journal_header.id == journal_header.id
    assert len(journal_line.linked_invoices) == 1
    assert journal_line.linked_invoices[0].id == linked_invoice.id


@pytest.fixture
def journal_line_with_account():
    return JournalLine(
        line_type=OperationTypeEnum.DEBIT,
        amount=100.00,
        account_id="123456",
    )


@pytest.fixture
def journal_line_without_account():
    return JournalLine(
        line_type=OperationTypeEnum.CREDIT,
        amount=100.00,
        account_id=None,
    )


def test_determine_status_with_account(journal_line_with_account):
    assert (
        journal_line_with_account.determine_status() == TransactionStatusEnum.RECONCILED
    )


def test_create_account(db_test):
    account_obj = Account(
        cnpj="**************",
        parent_code="123",
        description="Sample Account",
        classification="Classification A",
        level=1,
        short_code=123,
        status=ReconciliationStatusEnum.ACTIVE,
        account_type=AccountTypeEnum.ANALYTIC,
        operation_type=OperationTypeEnum.DEBIT,
    )
    account = account_obj.save()

    assert account == account_obj
