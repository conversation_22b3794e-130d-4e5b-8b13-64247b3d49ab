import pytest

from domain.models import Reconciliation, ReconciliationStatusEnum
from shared.session_manager import session_scope


@pytest.fixture
def create_reconciliation():
    def _create_reconciliation(status=ReconciliationStatusEnum.DRAFT):
        with session_scope() as session:
            reconciliation = Reconciliation(
                status=status,
                cockpit_customer_id="123",
                operator="test",
            )
            session.add(reconciliation)
            session.commit()
            return reconciliation

    return _create_reconciliation
