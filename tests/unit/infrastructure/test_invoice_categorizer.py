from types import SimpleNamespace

import pytest

from src.infrastructure.adapters.invoice_categorizer import InvoiceCategorizer


@pytest.fixture
def mock_journal_header():
    def build(amount, cpf_cnpj, name):
        partner_entity = SimpleNamespace(cpf_cnpj=cpf_cnpj, name=name)
        return SimpleNamespace(amount=amount, partner_entity=partner_entity)

    return build


@pytest.fixture
def categorizer():
    return InvoiceCategorizer(accounts=[])


def test_perfect_match(categorizer, mock_journal_header):
    """
    Test perfect match scenario: all fields (amount, document, and name) are
    identical. The score should be very high (>0.95), reflecting maximum
    confidence in the match.
    """
    header = mock_journal_header(100.0, "12.345.678/0001-99", "Empresa Exemplo")
    invoice = {
        "issuerDocument": "**************",
        "issuerName": "Empresa Exemplo",
        "amount": 100.0,
    }
    score = categorizer.calculate_score(header, invoice)
    assert score > 0.95


def test_force_match_by_doc_and_value(categorizer, mock_journal_header):
    """
    Test forced match scenario: document and amount are identical, but names are
    different. The business rule forces a high score (~0.98) if document and
    value match perfectly, regardless of name.
    """
    header = mock_journal_header(200.0, "**************", "Nome Qualquer")
    invoice = {
        "issuerDocument": "12.345.678/0001-99",
        "issuerName": "Outro Nome",
        "amount": 200.0,
    }
    score = categorizer.calculate_score(header, invoice)
    assert 0.97 < score < 0.99  # Forced by doc and value


def test_reject_by_name_and_doc(categorizer, mock_journal_header):
    """
    Test rejection scenario: both document and name are very different. The
    business rule returns a score of 0.0, indicating no match should be
    suggested.
    """
    header = mock_journal_header(100.0, "11111111000111", "Empresa X")
    invoice = {
        "issuerDocument": "22222222000122",
        "issuerName": "Empresa Y",
        "amount": 100.0,
    }
    score = categorizer.calculate_score(header, invoice)
    assert score == 0.0


def test_reject_by_value(categorizer, mock_journal_header):
    """
    Test rejection by value: document and name match, but the amount is very
    different. The score should be low (<0.7), reflecting low confidence due to
    value mismatch.
    """
    header = mock_journal_header(100.0, "**************", "Empresa Exemplo")
    invoice = {
        "issuerDocument": "**************",
        "issuerName": "Empresa Exemplo",
        "amount": 500.0,
    }
    score = categorizer.calculate_score(header, invoice)
    assert score < 0.7


def test_accept_by_strong_name_weak_doc(categorizer, mock_journal_header):
    """
    Test scenario where name matches strongly but document does not. According
    to the business rule, if both name and document are weak, the score is 0.0.
    """
    header = mock_journal_header(100.0, "11111111000111", "Empresa Exemplo")
    invoice = {
        "issuerDocument": "22222222000122",
        "issuerName": "Empresa Exemplo",
        "amount": 100.0,
    }
    score = categorizer.calculate_score(header, invoice)
    assert score == 0.0


def test_accept_by_strong_doc_weak_name(categorizer, mock_journal_header):
    """
    Test scenario where document matches strongly but name does not. The
    business rule allows a positive score (>0.5) if at least one of name or
    document is strong.
    """
    header = mock_journal_header(100.0, "**************", "Nome X")
    invoice = {
        "issuerDocument": "**************",
        "issuerName": "Outro Nome",
        "amount": 100.0,
    }
    score = categorizer.calculate_score(header, invoice)
    assert score > 0.5


def test_reject_both_weak(categorizer, mock_journal_header):
    """
    Test scenario where both name and document are weak (low similarity). The
    business rule returns a score of 0.0, indicating no match.
    """
    header = mock_journal_header(100.0, "11111111000111", "Nome X")
    invoice = {
        "issuerDocument": "22222222000122",
        "issuerName": "Outro Nome",
        "amount": 100.0,
    }
    score = categorizer.calculate_score(header, invoice)
    assert score == 0.0


def test_suggest_invoices_for_transaction_top_n(categorizer, mock_journal_header):
    """
    Test the ranking and filtering logic: given multiple invoices, only the top
    N with the highest scores are returned. Ensures the method returns the best
    matches in descending order of confidence.
    """
    header = mock_journal_header(100.0, "**************", "Empresa Exemplo")
    invoices = [
        {
            "issuerDocument": "**************",
            "issuerName": "Empresa Exemplo",
            "amount": 100.0,
        },
        {
            "issuerDocument": "**************",
            "issuerName": "Empresa Exemplo",
            "amount": 99.0,
        },
        {
            "issuerDocument": "11111111000111",
            "issuerName": "Empresa X",
            "amount": 100.0,
        },
    ]
    result = categorizer.suggest_invoices_for_transaction(
        header, iter(invoices), top_n=2
    )
    assert len(result) == 2
    assert result[0][1] >= result[1][1]  # Sorted by score


def test_invoice_with_missing_fields(categorizer, mock_journal_header):
    """
    Test scenario with missing/empty fields: both transaction and invoice have
    zero/empty values. The business rule forces a match (score 0.98) if
    document and value are both considered equal, even if empty.
    """
    header = mock_journal_header(0, None, None)
    invoice = {}
    score = categorizer.calculate_score(header, invoice)
    assert score == 0.98


def test_stats_are_updated(categorizer, mock_journal_header):
    """
    Test that the internal statistics counter is updated after each call to
    calculate_score. Ensures the categorizer tracks the number of processed
    transactions correctly.
    """
    header = mock_journal_header(100.0, "**************", "Empresa Exemplo")
    invoice = {
        "issuerDocument": "**************",
        "issuerName": "Empresa Exemplo",
        "amount": 100.0,
    }
    initial = categorizer.get_stats()["total_processed"]
    categorizer.calculate_score(header, invoice)
    assert categorizer.get_stats()["total_processed"] == initial + 1
