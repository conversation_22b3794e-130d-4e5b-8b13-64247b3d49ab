from unittest.mock import MagicMock, patch

import pytest

from domain.models import LinkedInvoice
from infrastructure.repositories.suggested_invoice import SuggestedInvoiceRepository
from shared.auth import Session


def mock_get_current_session():
    return Session(
        user_id="123",
        email="<EMAIL>",
        name="Test User",
        token="Bearer token",
    )


def test_get_by_account_id(db_test):
    cockpit_account_id = "12345"
    # mock_suggested_invoice = MagicMock()
    mock_session = mock_get_current_session()

    result = SuggestedInvoiceRepository(mock_session).get_by_account_id(cockpit_account_id)
    assert result
