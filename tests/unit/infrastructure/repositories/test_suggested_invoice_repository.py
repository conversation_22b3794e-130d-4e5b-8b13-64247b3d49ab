from decimal import Decimal
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
from sqlalchemy.exc import IntegrityError

from domain.exceptions import InvoiceNotFoundException
from domain.models import SuggestedInvoice, SuggestionSource, SuggestionTypeEnum
from infrastructure.repositories.invoice import InvoiceRepository
from shared.auth import Session


def mock_get_current_session():
    return Session(
        user_id="123",
        email="<EMAIL>",
        name="Test User",
        token="Bearer token",
    )


def create_mock_suggested_invoice(**kwargs):
    """Helper function to create a mock SuggestedInvoice with default values"""
    defaults = {
        "id": str(uuid4()),
        "suggestion_id": "suggestion_123",
        "cockpit_account_id": "account_123",
        "platform_transaction_id": "transaction_123",
        "platform_invoice_id": "invoice_123",
        "ranking": Decimal("1.0"),
        "exact_match": True,
        "should_not_be_matched": False,
        "version": "1.0",
        "invoice_date": "2024-01-01",
        "invoice_company_name": "Test Company",
        "invoice_value": "1000.00",
        "invoice_number": "INV-001",
        "source": SuggestionSource.BINDER,
        "account_data": {"test": "data"},
        "suggestion_type": SuggestionTypeEnum.INVOICE,
    }
    defaults.update(kwargs)
    return MagicMock(**defaults)


class TestSuggestedInvoiceRepository:
    """Test suite for SuggestedInvoiceRepository"""

    def setup_method(self):
        """Setup method called before each test"""
        self.mock_session = mock_get_current_session()
        self.repository = InvoiceRepository(self.mock_session)
        self.test_account_id = "test_account_123"

    @patch("domain.models.SuggestedInvoice.filter_by")
    def test_get_by_account_id_success(self, mock_filter_by):
        """Test successful retrieval of suggested invoices by account ID"""
        expected_invoices = [
            create_mock_suggested_invoice(cockpit_account_id=self.test_account_id),
            create_mock_suggested_invoice(cockpit_account_id=self.test_account_id),
        ]
        mock_filter_by.return_value = expected_invoices

        result = self.repository.get_by_cnpj(self.test_account_id)

        assert result == expected_invoices
        assert len(result) == 2
        mock_filter_by.assert_called_once_with(cockpit_account_id=self.test_account_id)

    @patch("domain.models.SuggestedInvoice.filter_by")
    def test_get_by_account_id_not_found_raises_exception(self, mock_filter_by):
        """Test that empty result raises SuggestedInvoiceNotFoundException"""
        mock_filter_by.return_value = []  # Empty list means no results found

        with pytest.raises(InvoiceNotFoundException) as exc_info:
            self.repository.get_by_cnpj(self.test_account_id)

        assert f"for account ID {self.test_account_id}" in str(exc_info.value)
        mock_filter_by.assert_called_once_with(cockpit_account_id=self.test_account_id)

    @patch("domain.models.SuggestedInvoice.filter_by")
    def test_get_by_account_id_generic_exception_returns_none(self, mock_filter_by):
        """Test that generic exceptions return None"""
        mock_filter_by.side_effect = Exception("Database connection error")

        result = self.repository.get_by_cnpj(self.test_account_id)

        assert result is None
        mock_filter_by.assert_called_once_with(cockpit_account_id=self.test_account_id)

    @patch("domain.models.SuggestedInvoice.filter_by")
    def test_get_by_account_id_single_result(self, mock_filter_by):
        """Test successful retrieval of single suggested invoice by account ID"""
        expected_invoices = [
            create_mock_suggested_invoice(cockpit_account_id=self.test_account_id)
        ]
        mock_filter_by.return_value = expected_invoices

        result = self.repository.get_by_cnpj(self.test_account_id)

        assert result == expected_invoices
        assert len(result) == 1
        mock_filter_by.assert_called_once_with(cockpit_account_id=self.test_account_id)

    def test_get_by_account_id_with_empty_string(self):
        """Test behavior with empty string account ID"""
        with patch("domain.models.SuggestedInvoice.filter_by") as mock_filter_by:
            mock_filter_by.return_value = []

            with pytest.raises(InvoiceNotFoundException):
                self.repository.get_by_cnpj("")

    def test_get_by_account_id_with_none(self):
        """Test behavior with None account ID"""
        with patch("domain.models.SuggestedInvoice.filter_by") as mock_filter_by:
            mock_filter_by.side_effect = TypeError("NoneType object")

            result = self.repository.get_by_cnpj(None)
            assert result is None

    def test_repository_initialization(self):
        """Test that repository initializes correctly with session"""
        repository = InvoiceRepository(self.mock_session)

        assert repository.session == self.mock_session
        assert hasattr(repository, "get_by_account_id")

    def test_repository_initialization_with_none_session(self):
        """Test repository initialization with None session"""
        repository = InvoiceRepository(None)
        assert repository.session is None

    @patch("domain.models.SuggestedInvoice.filter_by")
    def test_get_by_account_id_with_very_long_id(self, mock_filter_by):
        """Test behavior with very long account ID"""
        long_account_id = "a" * 1000
        expected_invoices = [
            create_mock_suggested_invoice(cockpit_account_id=long_account_id)
        ]
        mock_filter_by.return_value = expected_invoices

        result = self.repository.get_by_cnpj(long_account_id)

        assert result == expected_invoices
        mock_filter_by.assert_called_once_with(cockpit_account_id=long_account_id)

    @patch("domain.models.SuggestedInvoice.filter_by")
    def test_get_by_account_id_returns_correct_data_types(self, mock_filter_by):
        """Test that returned data has correct types"""
        expected_invoices = [create_mock_suggested_invoice()]
        mock_filter_by.return_value = expected_invoices

        result = self.repository.get_by_cnpj(self.test_account_id)

        assert result == expected_invoices
        assert len(result) == 1

        first_invoice = result[0]
        assert hasattr(first_invoice, "suggestion_id")
        assert hasattr(first_invoice, "cockpit_account_id")
        assert hasattr(first_invoice, "platform_transaction_id")
        assert hasattr(first_invoice, "ranking")
        assert hasattr(first_invoice, "exact_match")
        assert hasattr(first_invoice, "suggestion_type")

    @patch("domain.models.SuggestedInvoice.create")
    def test_create_suggested_invoice_with_valid_data(self, mock_create):
        """Test creating suggested invoice with valid data"""
        invoice_data = {
            "suggestion_id": "suggestion_456",
            "cockpit_account_id": "account_456",
            "platform_transaction_id": "transaction_456",
            "platform_invoice_id": "invoice_456",
            "ranking": Decimal("2.5"),
            "exact_match": False,
            "should_not_be_matched": False,
            "suggestion_type": SuggestionTypeEnum.ACCOUNT,
        }
        expected_invoice = create_mock_suggested_invoice(**invoice_data)
        mock_create.return_value = expected_invoice

        result = SuggestedInvoice.create(**invoice_data)

        assert result == expected_invoice
        mock_create.assert_called_once_with(**invoice_data)

    @patch("domain.models.SuggestedInvoice.create")
    def test_create_suggested_invoice_with_duplicate_constraint_violation(
        self, mock_create
    ):
        """Test creating suggested invoice with duplicate key constraint violation"""
        invoice_data = {
            "platform_transaction_id": "transaction_duplicate",
            "platform_invoice_id": "invoice_duplicate",
        }
        mock_create.side_effect = IntegrityError("Duplicate key violation", None, None)

        with pytest.raises(IntegrityError):
            SuggestedInvoice.create(**invoice_data)

    @patch("domain.models.SuggestedInvoice.filter_by")
    def test_multiple_concurrent_get_requests(self, mock_filter_by):
        """Test handling multiple concurrent get requests"""
        account_ids = ["account_1", "account_2", "account_3"]
        expected_results = [
            [create_mock_suggested_invoice(cockpit_account_id=aid)]
            for aid in account_ids
        ]
        mock_filter_by.side_effect = expected_results

        results = []
        for account_id in account_ids:
            result = self.repository.get_by_cnpj(account_id)
            results.append(result)

        assert len(results) == 3
        assert all(result is not None for result in results)
        assert all(len(result) == 1 for result in results)
        assert mock_filter_by.call_count == 3

    @patch("domain.models.SuggestedInvoice.bulk_save_objects")
    def test_bulk_operations(self, mock_bulk_save):
        """Test bulk save operations for multiple suggested invoices"""
        invoices_data = [
            create_mock_suggested_invoice(cockpit_account_id=f"account_{i}")
            for i in range(5)
        ]
        mock_bulk_save.return_value = None

        SuggestedInvoice.bulk_save_objects(invoices_data)

        mock_bulk_save.assert_called_once_with(invoices_data)

    @patch("domain.models.SuggestedInvoice.count")
    def test_count_suggested_invoices(self, mock_count):
        """Test counting total suggested invoices"""
        expected_count = 1000
        mock_count.return_value = expected_count

        result = SuggestedInvoice.count()

        assert result == expected_count
        mock_count.assert_called_once()

    @patch("domain.models.SuggestedInvoice.all")
    def test_get_all_suggested_invoices(self, mock_all):
        """Test retrieving all suggested invoices"""
        expected_invoices = [create_mock_suggested_invoice() for _ in range(10)]
        mock_all.return_value = expected_invoices

        result = SuggestedInvoice.all()

        assert result == expected_invoices
        assert len(result) == 10
        mock_all.assert_called_once()

    @patch("domain.models.SuggestedInvoice.filter_by")
    def test_get_by_account_id_with_complex_account_data(self, mock_filter_by):
        """Test handling complex JSON account_data"""
        complex_account_data = {
            "account_number": "*********",
            "account_type": "expense_account",
            "rules": [
                {"rule_name": "rule1", "rule_type": "automatic", "confidence": 0.95},
                {"rule_name": "rule2", "rule_type": "manual", "confidence": 0.80},
            ],
            "metadata": {
                "created_by": "system",
                "last_updated": "2024-01-01T10:00:00Z",
                "tags": ["important", "verified"],
            },
        }
        expected_invoices = [
            create_mock_suggested_invoice(account_data=complex_account_data)
        ]
        mock_filter_by.return_value = expected_invoices

        result = self.repository.get_by_cnpj(self.test_account_id)

        assert result == expected_invoices
        assert len(result) == 1
        assert result[0].account_data == complex_account_data

    def test_repository_session_attribute(self):
        """Test that repository properly stores session"""
        test_session = mock_get_current_session()

        repository = InvoiceRepository(test_session)

        assert repository.session == test_session
        assert repository.session.user_id == "123"
        assert repository.session.email == "<EMAIL>"

    @patch("domain.models.SuggestedInvoice.get_one_or_none")
    def test_get_one_or_none_functionality(self, mock_get_one_or_none):
        """Test get_one_or_none model method"""
        test_id = str(uuid4())
        expected_invoice = create_mock_suggested_invoice(id=test_id)
        mock_get_one_or_none.return_value = expected_invoice

        result = SuggestedInvoice.get_one_or_none(test_id)

        assert result == expected_invoice
        mock_get_one_or_none.assert_called_once_with(test_id)

    @patch("domain.models.SuggestedInvoice.get_one_or_none")
    def test_get_one_or_none_returns_none(self, mock_get_one_or_none):
        """Test get_one_or_none returns None for non-existent record"""
        test_id = str(uuid4())
        mock_get_one_or_none.return_value = None

        result = SuggestedInvoice.get_one_or_none(test_id)

        assert result is None
        mock_get_one_or_none.assert_called_once_with(test_id)


def test_get_by_account_id(db_test):
    """Legacy test function for backward compatibility"""
    cockpit_account_id = "12345"
    mock_session = mock_get_current_session()

    with patch("domain.models.SuggestedInvoice.filter_by") as mock_filter_by:
        mock_suggested_invoices = [
            create_mock_suggested_invoice(cockpit_account_id=cockpit_account_id)
        ]
        mock_filter_by.return_value = mock_suggested_invoices

        result = InvoiceRepository(mock_session).get_by_cnpj(
            cockpit_account_id
        )
        assert result == mock_suggested_invoices
        assert len(result) == 1
