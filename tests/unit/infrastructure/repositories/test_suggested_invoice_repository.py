from decimal import Decimal
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
from sqlalchemy.exc import NoResultFound, IntegrityError, DatabaseError

from domain.exceptions import SuggestedInvoiceNotFoundException
from domain.models import SuggestedInvoice, SuggestionSource, SuggestionTypeEnum
from infrastructure.repositories.suggested_invoice import SuggestedInvoiceRepository
from shared.auth import Session


def mock_get_current_session():
    return Session(
        user_id="123",
        email="<EMAIL>",
        name="Test User",
        token="Bearer token",
    )


def create_mock_suggested_invoice(**kwargs):
    """Helper function to create a mock SuggestedInvoice with default values"""
    defaults = {
        "id": str(uuid4()),
        "suggestion_id": "suggestion_123",
        "cockpit_account_id": "account_123",
        "platform_transaction_id": "transaction_123",
        "platform_invoice_id": "invoice_123",
        "ranking": Decimal("1.0"),
        "exact_match": True,
        "should_not_be_matched": False,
        "version": "1.0",
        "invoice_date": "2024-01-01",
        "invoice_company_name": "Test Company",
        "invoice_value": "1000.00",
        "invoice_number": "INV-001",
        "source": SuggestionSource.BINDER,
        "account_data": {"test": "data"},
        "suggestion_type": SuggestionTypeEnum.INVOICE,
    }
    defaults.update(kwargs)
    return MagicMock(**defaults)


class TestSuggestedInvoiceRepository:
    """Test suite for SuggestedInvoiceRepository"""

    def setup_method(self):
        """Setup method called before each test"""
        self.mock_session = mock_get_current_session()
        self.repository = SuggestedInvoiceRepository(self.mock_session)
        self.test_account_id = "test_account_123"

    # CRUD Operations Tests

    @patch('domain.models.SuggestedInvoice.get')
    def test_get_by_account_id_success(self, mock_get):
        """Test successful retrieval of suggested invoice by account ID"""
        # Arrange
        expected_invoice = create_mock_suggested_invoice(cockpit_account_id=self.test_account_id)
        mock_get.return_value = expected_invoice

        # Act
        result = self.repository.get_by_account_id(self.test_account_id)

        # Assert
        assert result == expected_invoice
        mock_get.assert_called_once_with(self.test_account_id)

    @patch('domain.models.SuggestedInvoice.get')
    def test_get_by_account_id_not_found_raises_exception(self, mock_get):
        """Test that NoResultFound raises SuggestedInvoiceNotFoundException"""
        # Arrange
        mock_get.side_effect = NoResultFound()

        # Act & Assert
        with pytest.raises(SuggestedInvoiceNotFoundException) as exc_info:
            self.repository.get_by_account_id(self.test_account_id)

        assert f"for account ID {self.test_account_id}" in str(exc_info.value)
        mock_get.assert_called_once_with(self.test_account_id)

    @patch('domain.models.SuggestedInvoice.get')
    def test_get_by_account_id_generic_exception_returns_none(self, mock_get):
        """Test that generic exceptions return None"""
        # Arrange
        mock_get.side_effect = Exception("Database connection error")

        # Act
        result = self.repository.get_by_account_id(self.test_account_id)

        # Assert
        assert result is None
        mock_get.assert_called_once_with(self.test_account_id)

    # Data Validation Tests

    def test_get_by_account_id_with_empty_string(self):
        """Test behavior with empty string account ID"""
        with patch('domain.models.SuggestedInvoice.get') as mock_get:
            mock_get.side_effect = NoResultFound()

            with pytest.raises(SuggestedInvoiceNotFoundException):
                self.repository.get_by_account_id("")

    def test_get_by_account_id_with_none(self):
        """Test behavior with None account ID"""
        with patch('domain.models.SuggestedInvoice.get') as mock_get:
            mock_get.side_effect = TypeError("NoneType object")

            result = self.repository.get_by_account_id(None)
            assert result is None

    def test_get_by_account_id_with_special_characters(self):
        """Test behavior with special characters in account ID"""
        special_account_id = "account@#$%^&*()_+-=[]{}|;':\",./<>?"
        with patch('domain.models.SuggestedInvoice.get') as mock_get:
            expected_invoice = create_mock_suggested_invoice(cockpit_account_id=special_account_id)
            mock_get.return_value = expected_invoice

            result = self.repository.get_by_account_id(special_account_id)
            assert result == expected_invoice

    # Error Handling Tests

    @patch('domain.models.SuggestedInvoice.get')
    def test_get_by_account_id_database_error(self, mock_get):
        """Test handling of database connection errors"""
        # Arrange
        mock_get.side_effect = DatabaseError("Connection lost", None, None)

        # Act
        result = self.repository.get_by_account_id(self.test_account_id)

        # Assert
        assert result is None

    @patch('domain.models.SuggestedInvoice.get')
    def test_get_by_account_id_integrity_error(self, mock_get):
        """Test handling of database integrity errors"""
        # Arrange
        mock_get.side_effect = IntegrityError("Constraint violation", None, None)

        # Act
        result = self.repository.get_by_account_id(self.test_account_id)

        # Assert
        assert result is None

    # Query Methods Tests (Testing potential filtering capabilities)

    @patch('domain.models.SuggestedInvoice.filter_by')
    def test_filter_by_platform_transaction_id(self, mock_filter_by):
        """Test filtering suggested invoices by platform transaction ID"""
        # Arrange
        transaction_id = "transaction_456"
        expected_invoices = [
            create_mock_suggested_invoice(platform_transaction_id=transaction_id),
            create_mock_suggested_invoice(platform_transaction_id=transaction_id)
        ]
        mock_filter_by.return_value = expected_invoices

        # Act - This would be a method we might add to the repository
        # For now, testing the underlying model capability
        result = SuggestedInvoice.filter_by(platform_transaction_id=transaction_id)

        # Assert
        assert result == expected_invoices
        mock_filter_by.assert_called_once_with(platform_transaction_id=transaction_id)

    @patch('domain.models.SuggestedInvoice.filter_by')
    def test_filter_by_suggestion_type(self, mock_filter_by):
        """Test filtering suggested invoices by suggestion type"""
        # Arrange
        suggestion_type = SuggestionTypeEnum.INVOICE
        expected_invoices = [create_mock_suggested_invoice(suggestion_type=suggestion_type)]
        mock_filter_by.return_value = expected_invoices

        # Act
        result = SuggestedInvoice.filter_by(suggestion_type=suggestion_type)

        # Assert
        assert result == expected_invoices
        mock_filter_by.assert_called_once_with(suggestion_type=suggestion_type)

    @patch('domain.models.SuggestedInvoice.filter_by')
    def test_filter_by_exact_match(self, mock_filter_by):
        """Test filtering suggested invoices by exact match flag"""
        # Arrange
        expected_invoices = [create_mock_suggested_invoice(exact_match=True)]
        mock_filter_by.return_value = expected_invoices

        # Act
        result = SuggestedInvoice.filter_by(exact_match=True)

        # Assert
        assert result == expected_invoices
        mock_filter_by.assert_called_once_with(exact_match=True)

    @patch('domain.models.SuggestedInvoice.filter_by')
    def test_filter_by_should_not_be_matched(self, mock_filter_by):
        """Test filtering suggested invoices by should_not_be_matched flag"""
        # Arrange
        expected_invoices = [create_mock_suggested_invoice(should_not_be_matched=True)]
        mock_filter_by.return_value = expected_invoices

        # Act
        result = SuggestedInvoice.filter_by(should_not_be_matched=True)

        # Assert
        assert result == expected_invoices
        mock_filter_by.assert_called_once_with(should_not_be_matched=True)

    @patch('domain.models.SuggestedInvoice.filter_one_by')
    def test_filter_one_by_multiple_criteria(self, mock_filter_one_by):
        """Test filtering single suggested invoice by multiple criteria"""
        # Arrange
        criteria = {
            "platform_transaction_id": "transaction_789",
            "platform_invoice_id": "invoice_789",
            "suggestion_type": SuggestionTypeEnum.INVOICE
        }
        expected_invoice = create_mock_suggested_invoice(**criteria)
        mock_filter_one_by.return_value = expected_invoice

        # Act
        result = SuggestedInvoice.filter_one_by(**criteria)

        # Assert
        assert result == expected_invoice
        mock_filter_one_by.assert_called_once_with(**criteria)

    # Repository Initialization Tests

    def test_repository_initialization(self):
        """Test that repository initializes correctly with session"""
        # Act
        repository = SuggestedInvoiceRepository(self.mock_session)

        # Assert
        assert repository.session == self.mock_session
        assert hasattr(repository, 'get_by_account_id')

    def test_repository_initialization_with_none_session(self):
        """Test repository initialization with None session"""
        # Act & Assert
        repository = SuggestedInvoiceRepository(None)
        assert repository.session is None

    # Edge Cases and Boundary Tests

    @patch('domain.models.SuggestedInvoice.get')
    def test_get_by_account_id_with_very_long_id(self, mock_get):
        """Test behavior with very long account ID"""
        # Arrange
        long_account_id = "a" * 1000  # Very long string
        expected_invoice = create_mock_suggested_invoice(cockpit_account_id=long_account_id)
        mock_get.return_value = expected_invoice

        # Act
        result = self.repository.get_by_account_id(long_account_id)

        # Assert
        assert result == expected_invoice
        mock_get.assert_called_once_with(long_account_id)

    @patch('domain.models.SuggestedInvoice.get')
    def test_get_by_account_id_with_unicode_characters(self, mock_get):
        """Test behavior with unicode characters in account ID"""
        # Arrange
        unicode_account_id = "账户_123_测试_🏦"
        expected_invoice = create_mock_suggested_invoice(cockpit_account_id=unicode_account_id)
        mock_get.return_value = expected_invoice

        # Act
        result = self.repository.get_by_account_id(unicode_account_id)

        # Assert
        assert result == expected_invoice
        mock_get.assert_called_once_with(unicode_account_id)

    # Data Consistency Tests

    @patch('domain.models.SuggestedInvoice.get')
    def test_get_by_account_id_returns_correct_data_types(self, mock_get):
        """Test that returned data has correct types"""
        # Arrange
        expected_invoice = create_mock_suggested_invoice()
        mock_get.return_value = expected_invoice

        # Act
        result = self.repository.get_by_account_id(self.test_account_id)

        # Assert
        assert result == expected_invoice
        # Verify the mock has the expected attributes
        assert hasattr(result, 'suggestion_id')
        assert hasattr(result, 'cockpit_account_id')
        assert hasattr(result, 'platform_transaction_id')
        assert hasattr(result, 'ranking')
        assert hasattr(result, 'exact_match')
        assert hasattr(result, 'suggestion_type')

    # Database Constraints Tests

    @patch('domain.models.SuggestedInvoice.create')
    def test_create_suggested_invoice_with_valid_data(self, mock_create):
        """Test creating suggested invoice with valid data"""
        # Arrange
        invoice_data = {
            "suggestion_id": "suggestion_456",
            "cockpit_account_id": "account_456",
            "platform_transaction_id": "transaction_456",
            "platform_invoice_id": "invoice_456",
            "ranking": Decimal("2.5"),
            "exact_match": False,
            "should_not_be_matched": False,
            "suggestion_type": SuggestionTypeEnum.ACCOUNT
        }
        expected_invoice = create_mock_suggested_invoice(**invoice_data)
        mock_create.return_value = expected_invoice

        # Act
        result = SuggestedInvoice.create(**invoice_data)

        # Assert
        assert result == expected_invoice
        mock_create.assert_called_once_with(**invoice_data)

    @patch('domain.models.SuggestedInvoice.create')
    def test_create_suggested_invoice_with_duplicate_constraint_violation(self, mock_create):
        """Test creating suggested invoice with duplicate key constraint violation"""
        # Arrange
        invoice_data = {
            "platform_transaction_id": "transaction_duplicate",
            "platform_invoice_id": "invoice_duplicate",
        }
        mock_create.side_effect = IntegrityError("Duplicate key violation", None, None)

        # Act & Assert
        with pytest.raises(IntegrityError):
            SuggestedInvoice.create(**invoice_data)

    # Relationship Handling Tests

    def test_suggested_invoice_enum_values(self):
        """Test that enum values are properly handled"""
        # Test SuggestionSource enum
        assert SuggestionSource.BINDER == "BINDER"

        # Test SuggestionTypeEnum enum
        assert SuggestionTypeEnum.ACCOUNT == "ACCOUNT"
        assert SuggestionTypeEnum.INVOICE == "INVOICE"
        assert SuggestionTypeEnum.INFO == "INFO"

    @patch('domain.models.SuggestedInvoice.filter_by')
    def test_filter_by_source_enum(self, mock_filter_by):
        """Test filtering by source enum value"""
        # Arrange
        source = SuggestionSource.BINDER
        expected_invoices = [create_mock_suggested_invoice(source=source)]
        mock_filter_by.return_value = expected_invoices

        # Act
        result = SuggestedInvoice.filter_by(source=source)

        # Assert
        assert result == expected_invoices
        mock_filter_by.assert_called_once_with(source=source)

    # Concurrent Processing Tests (Async-like scenarios)

    @patch('domain.models.SuggestedInvoice.get')
    def test_multiple_concurrent_get_requests(self, mock_get):
        """Test handling multiple concurrent get requests"""
        # Arrange
        account_ids = ["account_1", "account_2", "account_3"]
        expected_invoices = [
            create_mock_suggested_invoice(cockpit_account_id=aid) for aid in account_ids
        ]
        mock_get.side_effect = expected_invoices

        # Act - Simulate concurrent requests
        results = []
        for account_id in account_ids:
            result = self.repository.get_by_account_id(account_id)
            results.append(result)

        # Assert
        assert len(results) == 3
        assert all(result is not None for result in results)
        assert mock_get.call_count == 3

    @patch('domain.models.SuggestedInvoice.bulk_save_objects')
    def test_bulk_operations(self, mock_bulk_save):
        """Test bulk save operations for multiple suggested invoices"""
        # Arrange
        invoices_data = [
            create_mock_suggested_invoice(cockpit_account_id=f"account_{i}")
            for i in range(5)
        ]
        mock_bulk_save.return_value = None

        # Act
        SuggestedInvoice.bulk_save_objects(invoices_data)

        # Assert
        mock_bulk_save.assert_called_once_with(invoices_data)

    # Performance and Scalability Tests

    @patch('domain.models.SuggestedInvoice.count')
    def test_count_suggested_invoices(self, mock_count):
        """Test counting total suggested invoices"""
        # Arrange
        expected_count = 1000
        mock_count.return_value = expected_count

        # Act
        result = SuggestedInvoice.count()

        # Assert
        assert result == expected_count
        mock_count.assert_called_once()

    @patch('domain.models.SuggestedInvoice.all')
    def test_get_all_suggested_invoices(self, mock_all):
        """Test retrieving all suggested invoices"""
        # Arrange
        expected_invoices = [create_mock_suggested_invoice() for _ in range(10)]
        mock_all.return_value = expected_invoices

        # Act
        result = SuggestedInvoice.all()

        # Assert
        assert result == expected_invoices
        assert len(result) == 10
        mock_all.assert_called_once()

    # JSON Data Handling Tests

    @patch('domain.models.SuggestedInvoice.get')
    def test_get_by_account_id_with_complex_account_data(self, mock_get):
        """Test handling complex JSON account_data"""
        # Arrange
        complex_account_data = {
            "account_number": "*********",
            "account_type": "expense_account",
            "rules": [
                {"rule_name": "rule1", "rule_type": "automatic", "confidence": 0.95},
                {"rule_name": "rule2", "rule_type": "manual", "confidence": 0.80}
            ],
            "metadata": {
                "created_by": "system",
                "last_updated": "2024-01-01T10:00:00Z",
                "tags": ["important", "verified"]
            }
        }
        expected_invoice = create_mock_suggested_invoice(account_data=complex_account_data)
        mock_get.return_value = expected_invoice

        # Act
        result = self.repository.get_by_account_id(self.test_account_id)

        # Assert
        assert result == expected_invoice
        assert result.account_data == complex_account_data

    # Session Management Tests

    def test_repository_session_attribute(self):
        """Test that repository properly stores session"""
        # Arrange
        test_session = mock_get_current_session()

        # Act
        repository = SuggestedInvoiceRepository(test_session)

        # Assert
        assert repository.session == test_session
        assert repository.session.user_id == "123"
        assert repository.session.email == "<EMAIL>"

    # Integration-style Tests (testing model methods through repository)

    @patch('domain.models.SuggestedInvoice.get_one_or_none')
    def test_get_one_or_none_functionality(self, mock_get_one_or_none):
        """Test get_one_or_none model method"""
        # Arrange
        test_id = str(uuid4())
        expected_invoice = create_mock_suggested_invoice(id=test_id)
        mock_get_one_or_none.return_value = expected_invoice

        # Act
        result = SuggestedInvoice.get_one_or_none(test_id)

        # Assert
        assert result == expected_invoice
        mock_get_one_or_none.assert_called_once_with(test_id)

    @patch('domain.models.SuggestedInvoice.get_one_or_none')
    def test_get_one_or_none_returns_none(self, mock_get_one_or_none):
        """Test get_one_or_none returns None for non-existent record"""
        # Arrange
        test_id = str(uuid4())
        mock_get_one_or_none.return_value = None

        # Act
        result = SuggestedInvoice.get_one_or_none(test_id)

        # Assert
        assert result is None
        mock_get_one_or_none.assert_called_once_with(test_id)


# Legacy test function to maintain compatibility
def test_get_by_account_id(db_test):
    """Legacy test function for backward compatibility"""
    cockpit_account_id = "12345"
    mock_session = mock_get_current_session()

    with patch('domain.models.SuggestedInvoice.get') as mock_get:
        mock_suggested_invoice = create_mock_suggested_invoice(cockpit_account_id=cockpit_account_id)
        mock_get.return_value = mock_suggested_invoice

        result = SuggestedInvoiceRepository(mock_session).get_by_account_id(cockpit_account_id)
        assert result == mock_suggested_invoice
