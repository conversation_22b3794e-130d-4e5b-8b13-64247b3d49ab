import responses

from infrastructure.repositories.account_configuration_repository import (
    AccountConfigurationRepository,
)
from infrastructure.repositories.partner_entity_repository import (
    PartnerEntityRepository,
)
from infrastructure.repositories.platform_transaction_repository import (
    PlatformTransactionRepository,
)
from shared.auth import Session


def mock_get_current_session():
    return Session(
        user_id="123",
        email="<EMAIL>",
        name="Test User",
        token="Bearer token",
    )


@responses.activate
def test_get_journal_headers_by_bankaccounts(db_test):
    """Ensures fetching transactions results in correctly created
    JournalHeaders when feature flag is enabled."""

    mockSession = mock_get_current_session()
    responses._add_from_file(file_path="tests/fixtures/transactions_api.yaml")

    bank_accounts = ["37d0873a-e07a-11ee-ba2b-0a7ea7812cad"]
    competence = "2024-09"

    platform_transaction_repository = PlatformTransactionRepository(
        mockSession, PartnerEntityRepository(), AccountConfigurationRepository()
    )

    (
        journal_headers,
        _,
    ) = platform_transaction_repository.get_transactions_by_bankaccounts(
        bank_account_guids=bank_accounts, competence=competence
    )

    assert len(journal_headers) == 3

    expected_references = [
        "7c1ac25d-a406-4b5b-888e-55702d771d34",
        "dba61446-9110-4253-813b-c8d8e1e019ee",
        "ee8bb59a-bcfb-4ec2-b4fe-ab7b58c327fc",
    ]
    expected_cpf_cnpj = [
        "***********",
        None,
        "13.399.454/0001-29",
    ]

    expected_amounts = [1.64, -16450.85, -630]
    expected_descriptions = [
        "PIX TRANSF LUCAS",
        "SISPAG CONSOLIDADO",
        "SISPAG CONSOLIDADO",
    ]

    for i, journal in enumerate(journal_headers):
        cpf_cnpj = journal.partner_entity.cpf_cnpj if journal.partner_entity else None

        assert journal.source_reference_id == expected_references[i]
        assert journal.amount == expected_amounts[i]
        assert journal.description == expected_descriptions[i]
        assert cpf_cnpj == expected_cpf_cnpj[i]
        assert len(journal.journal_lines) == 2

        debit_line = next(
            (line for line in journal.journal_lines if line.line_type == "DEBIT"), None
        )
        credit_line = next(
            (line for line in journal.journal_lines if line.line_type == "CREDIT"), None
        )

        assert debit_line.description == journal.description
        assert debit_line.amount == abs(expected_amounts[i])

        assert credit_line.description == journal.description
        assert credit_line.amount == abs(expected_amounts[i])
