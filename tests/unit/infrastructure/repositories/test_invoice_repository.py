from datetime import date, datetime
from decimal import Decimal
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
from dateutil.relativedelta import relativedelta

from domain.exceptions import InvoiceNotFoundException
from domain.models import Invoice, InvoiceTypeEnum, InvoiceStatusEnum
from infrastructure.repositories.invoice import InvoiceRepository
from shared.auth import Session


def mock_get_current_session():
    return Session(
        user_id="123",
        email="<EMAIL>",
        name="Test User",
        token="Bearer token",
    )


def create_mock_invoice(**kwargs):
    """Helper function to create a mock Invoice with default values"""
    defaults = {
        "id": str(uuid4()),
        "type": InvoiceTypeEnum.NFE,
        "number": "INV-001",
        "status": InvoiceStatusEnum.ISSUED,
        "issue_date": datetime.now(),
        "competence_date": date.today(),
        "issuer_name": "Test Issuer Company",
        "issuer_document": "12345678000100",
        "taker_name": "Test Taker Company",
        "taker_document": "98765432000100",
        "customer_document": "11111111000100",
        "amount": Decimal("1000.00"),
        "net_amount": Decimal("900.00"),
    }
    defaults.update(kwargs)
    return MagicMock(**defaults)


class TestInvoiceRepository:
    """Test suite for InvoiceRepository"""

    def setup_method(self):
        """Setup method called before each test"""
        self.mock_session = mock_get_current_session()
        self.repository = InvoiceRepository(self.mock_session)
        self.test_taker_document = "12345678000100"
        self.test_issuer_document = "98765432000100"

    # CRUD Operations Tests

    @patch("domain.models.Invoice.filter")
    def test_get_by_cnpj_with_taker_document_success(self, mock_filter):
        """Test successful retrieval of invoices by taker document"""
        # Arrange
        expected_invoices = [
            create_mock_invoice(taker_document=self.test_taker_document),
            create_mock_invoice(taker_document=self.test_taker_document)
        ]
        mock_filter.return_value = expected_invoices

        # Act
        result = self.repository.get_by_cnpj(taker_document=self.test_taker_document)

        # Assert
        assert result == expected_invoices
        assert len(result) == 2
        mock_filter.assert_called_once()

    @patch("domain.models.Invoice.filter")
    def test_get_by_cnpj_with_issuer_document_success(self, mock_filter):
        """Test successful retrieval of invoices by issuer document"""
        # Arrange
        expected_invoices = [create_mock_invoice(issuer_document=self.test_issuer_document)]
        mock_filter.return_value = expected_invoices

        # Act
        result = self.repository.get_by_cnpj(issuer_document=self.test_issuer_document)

        # Assert
        assert result == expected_invoices
        assert len(result) == 1
        mock_filter.assert_called_once()

    @patch("domain.models.Invoice.filter")
    def test_get_by_cnpj_with_both_documents_success(self, mock_filter):
        """Test successful retrieval of invoices with both taker and issuer documents"""
        # Arrange
        expected_invoices = [
            create_mock_invoice(
                taker_document=self.test_taker_document,
                issuer_document=self.test_issuer_document
            )
        ]
        mock_filter.return_value = expected_invoices

        # Act
        result = self.repository.get_by_cnpj(
            taker_document=self.test_taker_document,
            issuer_document=self.test_issuer_document
        )

        # Assert
        assert result == expected_invoices
        assert len(result) == 1
        mock_filter.assert_called_once()

    @patch("domain.models.Invoice.filter")
    def test_get_by_cnpj_with_competence_date(self, mock_filter):
        """Test retrieval with specific competence date"""
        # Arrange
        competence = "2024-01"
        expected_invoices = [create_mock_invoice(taker_document=self.test_taker_document)]
        mock_filter.return_value = expected_invoices

        # Act
        result = self.repository.get_by_cnpj(
            taker_document=self.test_taker_document,
            competence=competence
        )

        # Assert
        assert result == expected_invoices
        mock_filter.assert_called_once()

    # Error Handling Tests

    def test_get_by_cnpj_no_documents_raises_value_error(self):
        """Test that providing no documents raises ValueError"""
        # Act & Assert
        with pytest.raises(ValueError) as exc_info:
            self.repository.get_by_cnpj()
        
        assert "Either taker_document or issuer_document must be provided" in str(exc_info.value)

    @patch("domain.models.Invoice.filter")
    def test_get_by_cnpj_not_found_raises_exception(self, mock_filter):
        """Test that empty result raises InvoiceNotFoundException"""
        # Arrange
        mock_filter.return_value = []  # Empty list means no results found

        # Act & Assert
        with pytest.raises(InvoiceNotFoundException) as exc_info:
            self.repository.get_by_cnpj(taker_document=self.test_taker_document)
        
        assert "within the last 3 months" in str(exc_info.value)
        mock_filter.assert_called_once()

    @patch("domain.models.Invoice.filter")
    def test_get_by_cnpj_database_error_returns_none(self, mock_filter):
        """Test that database errors return None"""
        # Arrange
        mock_filter.side_effect = Exception("Database connection error")

        # Act
        result = self.repository.get_by_cnpj(taker_document=self.test_taker_document)

        # Assert
        assert result is None
        mock_filter.assert_called_once()

    # Date Range Tests

    @patch("domain.models.Invoice.filter")
    @patch("infrastructure.repositories.invoice.date")
    def test_get_by_cnpj_date_range_calculation(self, mock_date, mock_filter):
        """Test that date range is calculated correctly for last 3 months"""
        # Arrange
        current_date = date(2024, 6, 15)
        mock_date.today.return_value = current_date
        expected_start_date = current_date - relativedelta(months=3)  # 2024-03-15
        
        expected_invoices = [create_mock_invoice(taker_document=self.test_taker_document)]
        mock_filter.return_value = expected_invoices

        # Act
        result = self.repository.get_by_cnpj(taker_document=self.test_taker_document)

        # Assert
        assert result == expected_invoices
        mock_filter.assert_called_once()
        
        # Verify the filter was called with correct date range
        call_args = mock_filter.call_args[0]
        assert len(call_args) == 3  # Should have 3 filter conditions

    @patch("domain.models.Invoice.filter")
    def test_get_by_cnpj_with_invalid_competence_format(self, mock_filter):
        """Test handling of invalid competence date format"""
        # Act & Assert
        with pytest.raises(ValueError):
            self.repository.get_by_cnpj(
                taker_document=self.test_taker_document,
                competence="invalid-date"
            )

    # Repository Initialization Tests

    def test_repository_initialization(self):
        """Test that repository initializes correctly with session"""
        # Act
        repository = InvoiceRepository(self.mock_session)

        # Assert
        assert repository.session == self.mock_session
        assert hasattr(repository, 'get_by_cnpj')

    def test_repository_initialization_with_none_session(self):
        """Test repository initialization with None session"""
        # Act & Assert
        repository = InvoiceRepository(None)
        assert repository.session is None

    # Edge Cases and Boundary Tests

    @patch("domain.models.Invoice.filter")
    def test_get_by_cnpj_with_empty_string_documents(self, mock_filter):
        """Test behavior with empty string documents"""
        # Act & Assert
        with pytest.raises(ValueError):
            self.repository.get_by_cnpj(taker_document="", issuer_document="")

    @patch("domain.models.Invoice.filter")
    def test_get_by_cnpj_with_none_documents(self, mock_filter):
        """Test behavior with None documents"""
        # Act & Assert
        with pytest.raises(ValueError):
            self.repository.get_by_cnpj(taker_document=None, issuer_document=None)

    @patch("domain.models.Invoice.filter")
    def test_get_by_cnpj_with_special_characters_in_document(self, mock_filter):
        """Test behavior with special characters in document"""
        special_document = "12.345.678/0001-00"
        expected_invoices = [create_mock_invoice(taker_document=special_document)]
        mock_filter.return_value = expected_invoices

        result = self.repository.get_by_cnpj(taker_document=special_document)
        assert result == expected_invoices

    # Logging Tests

    @patch("domain.models.Invoice.filter")
    @patch("infrastructure.repositories.invoice.logger")
    def test_get_by_cnpj_logs_success(self, mock_logger, mock_filter):
        """Test that successful retrieval is logged"""
        # Arrange
        expected_invoices = [create_mock_invoice(taker_document=self.test_taker_document)]
        mock_filter.return_value = expected_invoices

        # Act
        result = self.repository.get_by_cnpj(taker_document=self.test_taker_document)

        # Assert
        assert result == expected_invoices
        mock_logger.info.assert_called_once()
        assert "Found 1 invoices" in mock_logger.info.call_args[0][0]

    @patch("domain.models.Invoice.filter")
    @patch("infrastructure.repositories.invoice.logger")
    def test_get_by_cnpj_logs_error(self, mock_logger, mock_filter):
        """Test that errors are logged"""
        # Arrange
        mock_filter.side_effect = Exception("Database error")

        # Act
        result = self.repository.get_by_cnpj(taker_document=self.test_taker_document)

        # Assert
        assert result is None
        mock_logger.error.assert_called_once()
        assert "Error retrieving invoices" in mock_logger.error.call_args[0][0]
