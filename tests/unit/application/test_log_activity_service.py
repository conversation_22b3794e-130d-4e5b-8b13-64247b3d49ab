from datetime import datetime
from decimal import Decimal

from domain.models import (
    Activity<PERSON>og,
    JournalHeader,
    JournalLine,
    PartnerEntity,
    Reconciliation,
    ReconciliationStatusEnum,
)


def test_activity_log(db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.ACTIVE,
        cockpit_customer_id="456",
        operator="Operator A",
    )
    reconciliation.save()

    journal_header = JournalHeader(
        amount=Decimal("2500.75"),
        date=datetime(2024, 3, 16),
        description="Sample Journal Header",
        notes="Test notes",
        status="RECONCILED",
        partner_entity=PartnerEntity(cpf_cnpj="**************", name="Cliente XYZ"),
        journal_lines=[
            JournalLine(
                line_type="DEBIT",
                account_id="102",
            ),
            JournalLine(
                line_type="CREDIT",
                account_id="202",
            ),
        ],
        reconciliation_id=reconciliation.id,
    )
    journal_header.save()

    activities = ActivityLog().all()

    assert len(activities) == 4
    reconciliation_logs = [a for a in activities if a.subject_type == "Reconciliation"]
    journal_header_logs = [a for a in activities if a.subject_type == "JournalHeader"]
    journal_lines_logs = [a for a in activities if a.subject_type == "JournalLine"]

    assert len(reconciliation_logs) == 1
    assert reconciliation_logs[0].event == "created"
    assert reconciliation_logs[0].description == "Reconciliation was created"

    assert len(journal_header_logs) == 1
    assert journal_header_logs[0].event == "created"
    assert journal_header_logs[0].description == "JournalHeader was created"

    assert len(journal_lines_logs) == 2
    assert journal_lines_logs[0].event == "created"
    assert journal_lines_logs[0].description == "JournalLine was created"
    assert journal_lines_logs[1].event == "created"
    assert journal_lines_logs[1].description == "JournalLine was created"

    journal_header.status = "NEW"
    journal_header.save()

    activities = ActivityLog().all()

    journal_header_logs = [a for a in activities if a.subject_type == "JournalHeader"]

    assert len(activities) == 5
    assert len(journal_header_logs) == 2
    assert journal_header_logs[0].event == "created"
    assert journal_header_logs[0].description == "JournalHeader was created"
    assert journal_header_logs[1].event == "updated"
    assert journal_header_logs[1].subject_type == "JournalHeader"
    assert journal_header_logs[1].description == "JournalHeader was updated"
    assert journal_header_logs[1].causer_id == "system"
    assert journal_header_logs[1].causer_type == "system"
