import base64
from datetime import datetime
from decimal import Decimal
from unittest.mock import Mock, patch

import pytest

from application.reconciliation_service import download_txt_file
from domain.exceptions import (
    ReconciliationNotFinishedException,
    ReconciliationNotFoundException,
)
from domain.models import (
    AccountConfiguration,
    AccountConfigurationTypeEnum,
    InvoiceMotiveEnum,
    JournalHeader,
    JournalLine,
    LinkedInvoice,
    PartnerEntity,
    Reconciliation,
    ReconciliationStatusEnum,
)


@pytest.fixture
def mock_reconciliation():
    return Reconciliation(
        id="rec123",
        cockpit_customer_id="cust123",
        customer_cnpj="**************",
        status=ReconciliationStatusEnum.FINISHED,
        journal_headers=[
            JournalHeader(
                amount=Decimal("-1000.50"),
                date=datetime(2024, 3, 15),
                description="Sample Journal Header",
                notes="Test notes",
                status="RECONCILED",
                partner_entity=PartnerEntity(
                    cpf_cnpj="**************", name="Fornecedor ABC"
                ),
                journal_lines=[
                    JournalLine(
                        line_type="DEBIT",
                        account_id="101",
                    ),
                    JournalLine(
                        line_type="CREDIT",
                        account_id="201",
                    ),
                ],
            ),
            JournalHeader(
                amount=Decimal("2500.75"),
                date=datetime(2024, 3, 16),
                description="Sample Journal Header",
                notes="Test notes",
                status="RECONCILED",
                partner_entity=PartnerEntity(
                    cpf_cnpj="**************", name="Cliente XYZ"
                ),
                journal_lines=[
                    JournalLine(
                        line_type="DEBIT",
                        account_id="102",
                    ),
                    JournalLine(
                        line_type="CREDIT",
                        account_id="202",
                    ),
                ],
            ),
        ],
    )


def test_download_txt_file_success_no_metadata(mock_reconciliation):
    mock_repository = Mock()
    mock_repository.get_reconciliation.return_value = mock_reconciliation

    with patch(
        "application.reconciliation_service.ReconciliationRepository",
        return_value=mock_repository,
    ):
        result = download_txt_file("rec123")

        decoded_content = base64.b64decode(result).decode("utf-8")
        lines = decoded_content.split("\n")

        assert lines[0] == "|0000|**************|"
        assert lines[1] == "|6000|X||||"
        assert (
            lines[2]
            == "|6100|15/03/2024|101|201|1000,50||PAGAMENTO - SAMPLE JOURNAL HEADER - FORNECEDOR ABC||||"  # noqa
        )
        assert lines[3] == "|6000|X||||"
        assert (
            lines[4]
            == "|6100|16/03/2024|102|202|2500,75||RECEBIMENTO - SAMPLE JOURNAL HEADER - CLIENTE XYZ||||"  # noqa
        )


def test_download_txt_file_success_invoice_associated(mock_reconciliation):
    mock_reconciliation.journal_headers[0].journal_lines[0]._metadata = {
        "has_linked_invoices": None,
        "is_linked_invoice_applicable": None,
        "linked_invoices": [1, 2, 3],
        "invoice_motive": InvoiceMotiveEnum.MISSING_INVOICE,
    }

    mock_repository = Mock()
    mock_repository.get_reconciliation.return_value = mock_reconciliation

    with patch(
        "application.reconciliation_service.ReconciliationRepository",
        return_value=mock_repository,
    ):
        result = download_txt_file("rec123")

        decoded_content = base64.b64decode(result).decode("utf-8")
        lines = decoded_content.split("\n")

        assert lines[0] == "|0000|**************|"
        assert lines[1] == "|6000|X||||"
        assert (
            lines[2]
            == "|6100|15/03/2024|101|201|1000,50||PAGAMENTO - FORNECEDOR ABC - SAMPLE JOURNAL HEADER||||"  # noqa
        )
        assert lines[3] == "|6000|X||||"
        assert (
            lines[4]
            == "|6100|16/03/2024|102|202|2500,75||RECEBIMENTO - SAMPLE JOURNAL HEADER - CLIENTE XYZ||||"  # noqa
        )


def test_download_txt_file_success_missing_invoice(mock_reconciliation):
    mock_reconciliation.journal_headers[0].journal_lines[0]._metadata = {
        "has_linked_invoices": None,
        "is_linked_invoice_applicable": False,
        "linked_invoices": [],
        "invoice_motive": InvoiceMotiveEnum.MISSING_INVOICE,
    }

    mock_repository = Mock()
    mock_repository.get_reconciliation.return_value = mock_reconciliation

    with patch(
        "application.reconciliation_service.ReconciliationRepository",
        return_value=mock_repository,
    ):
        result = download_txt_file("rec123")

        decoded_content = base64.b64decode(result).decode("utf-8")
        lines = decoded_content.split("\n")

        assert lines[0] == "|0000|**************|"
        assert lines[1] == "|6000|X||||"
        assert (
            lines[2]
            == "|6100|15/03/2024|101|201|1000,50||PAGAMENTO - NÃO SE APLICA - FORNECEDOR ABC - SAMPLE JOURNAL HEADER||||"  # noqa
        )
        assert lines[3] == "|6000|X||||"
        assert (
            lines[4]
            == "|6100|16/03/2024|102|202|2500,75||RECEBIMENTO - SAMPLE JOURNAL HEADER - CLIENTE XYZ||||"  # noqa
        )


def test_download_txt_file_success_pending_invoice(mock_reconciliation):
    mock_reconciliation.journal_headers[0].journal_lines[0]._metadata = {
        "has_linked_invoices": False,
        "is_linked_invoice_applicable": None,
        "linked_invoices": [],
    }

    mock_repository = Mock()
    mock_repository.get_reconciliation.return_value = mock_reconciliation

    with patch(
        "application.reconciliation_service.ReconciliationRepository",
        return_value=mock_repository,
    ):
        result = download_txt_file("rec123")

        decoded_content = base64.b64decode(result).decode("utf-8")
        lines = decoded_content.split("\n")

        assert lines[0] == "|0000|**************|"
        assert lines[1] == "|6000|X||||"
        assert (
            lines[2]
            == "|6100|15/03/2024|101|201|1000,50||PAGAMENTO - SAMPLE JOURNAL HEADER - FORNECEDOR ABC - TEST NOTES||||"  # noqa
        )
        assert lines[3] == "|6000|X||||"
        assert (
            lines[4]
            == "|6100|16/03/2024|102|202|2500,75||RECEBIMENTO - SAMPLE JOURNAL HEADER - CLIENTE XYZ||||"  # noqa
        )


def test_download_txt_file_success_not_invoice_not_missing(mock_reconciliation):
    mock_reconciliation.journal_headers[0].journal_lines[0]._metadata = {
        "has_linked_invoices": None,
        "is_linked_invoice_applicable": False,
        "linked_invoices": [],
        "invoice_motive": InvoiceMotiveEnum.BANK_FEE,
    }

    mock_repository = Mock()
    mock_repository.get_reconciliation.return_value = mock_reconciliation

    with patch(
        "application.reconciliation_service.ReconciliationRepository",
        return_value=mock_repository,
    ):
        result = download_txt_file("rec123")

        decoded_content = base64.b64decode(result).decode("utf-8")
        lines = decoded_content.split("\n")

        assert lines[0] == "|0000|**************|"
        assert lines[1] == "|6000|X||||"
        assert (
            lines[2]
            == "|6100|15/03/2024|101|201|1000,50||PAGAMENTO - TARIFA_BANCARIA - FORNECEDOR ABC - SAMPLE JOURNAL HEADER - TEST NOTES||||"  # noqa
        )
        assert lines[3] == "|6000|X||||"
        assert (
            lines[4]
            == "|6100|16/03/2024|102|202|2500,75||RECEBIMENTO - SAMPLE JOURNAL HEADER - CLIENTE XYZ||||"  # noqa
        )


def test_download_txt_with_account_configuration():
    linked_invoice1 = LinkedInvoice(
        platform_invoice_id="INV789",
        link_date=datetime.now(),
        link_percentual=75.0,
        invoice_number="INV003",
        file_path="/path/to/yet_another_invoice.pdf",
    ).save()

    linked_invoice2 = LinkedInvoice(
        platform_invoice_id="INV789",
        link_date=datetime.now(),
        link_percentual=75.0,
        invoice_number="INV004",
        file_path="/path/to/yet_another_invoice.pdf",
    ).save()

    linked_invoice3 = LinkedInvoice(
        platform_invoice_id="INV789",
        link_date=datetime.now(),
        link_percentual=75.0,
        invoice_number="INV005",
        file_path="/path/to/yet_another_invoice.pdf",
    ).save()

    reconciliation = Reconciliation(
        cockpit_customer_id="cust123",
        customer_cnpj="**************",
        status=ReconciliationStatusEnum.FINISHED,
        journal_headers=[
            JournalHeader(
                amount=Decimal("-1000.50"),
                date=datetime(2024, 3, 15),
                description="Sample Journal Header",
                notes="Test notes",
                status="RECONCILED",
                partner_entity=PartnerEntity(
                    cpf_cnpj="**************", name="Fornecedor ABC"
                ),
                journal_lines=[
                    JournalLine(
                        line_type="DEBIT",
                        account_id="101",
                        _metadata={
                            "has_linked_invoices": None,
                            "is_linked_invoice_applicable": None,
                            "linked_invoices": [],
                        },
                    ),
                    JournalLine(
                        line_type="CREDIT",
                        account_id="201",
                        _metadata={
                            "has_linked_invoices": None,
                            "is_linked_invoice_applicable": None,
                            "linked_invoices": [],
                        },
                    ),
                ],
                cockpit_account_id="123456",
            ),
            JournalHeader(
                amount=Decimal("5000.00"),
                date=datetime(2024, 3, 16),
                description="Sample Journal Header",
                notes="Test notes",
                status="RECONCILED",
                partner_entity=PartnerEntity(
                    cpf_cnpj="**************", name="Cliente XYZ"
                ),
                journal_lines=[
                    JournalLine(
                        line_type="DEBIT",
                        account_id="102",
                        amount=Decimal(5000),
                        linked_invoices=[linked_invoice1],
                        description="Sample Journal Line",
                        _metadata={
                            "has_linked_invoices": None,
                            "is_linked_invoice_applicable": None,
                            "linked_invoices": [],
                        },
                    ),
                    JournalLine(
                        line_type="CREDIT",
                        account_id="202",
                        amount=Decimal(4500),
                        linked_invoices=[linked_invoice2, linked_invoice3],
                        description="Sample Journal Line",
                        _metadata={
                            "has_linked_invoices": False,
                            "is_linked_invoice_applicable": None,
                            "linked_invoices": [],
                        },
                    ),
                    JournalLine(
                        line_type="CREDIT",
                        account_id="202",
                        amount=Decimal(500),
                        description="Sample Journal Line",
                        _metadata={
                            "has_linked_invoices": None,
                            "is_linked_invoice_applicable": None,
                            "linked_invoices": [],
                        },
                    ),
                ],
                cockpit_account_id="123456",
            ),
        ],
    ).save()

    AccountConfiguration(
        cockpit_account_id="123456",
        value="400",
        type=AccountConfigurationTypeEnum.CHART_OF_ACCOUNTS_CODE,
        reconciliation_id=reconciliation.id,
    ).save()

    result = download_txt_file(reconciliation.id)

    decoded_content = base64.b64decode(result).decode("utf-8")
    lines = decoded_content.split("\n")

    assert lines[0] == "|0000|**************|"

    assert lines[1] == "|6000|X||||"
    assert (
        lines[2]
        == "|6100|15/03/2024|101|201|1000,50||PAGAMENTO - FORNECEDOR ABC - SAMPLE JOURNAL HEADER - TEST NOTES||||"  # noqa
    )

    assert lines[3] == "|6000|V||||"
    assert (
        lines[4]
        == "|6100|16/03/2024|102||5000,00||PAGAMENTO SAMPLE JOURNAL LINE - CLIENTE XYZ - SAMPLE JOURNAL HEADER - TEST NOTES||||"  # noqa
    )
    assert (
        lines[5]
        == "|6100|16/03/2024||202|4500,00||RECEBIMENTO SAMPLE JOURNAL LINE - SAMPLE JOURNAL HEADER - CLIENTE XYZ - TEST NOTES||||"  # noqa
    )
    assert (
        lines[6]
        == "|6100|16/03/2024||202|500,00||RECEBIMENTO SAMPLE JOURNAL LINE - CLIENTE XYZ - SAMPLE JOURNAL HEADER - TEST NOTES||||"  # noqa
    )


def test_download_txt_file_not_found():
    mock_repository = Mock()
    mock_repository.get_reconciliation.return_value = None

    with patch(
        "application.reconciliation_service.ReconciliationRepository",
        return_value=mock_repository,
    ):
        with pytest.raises(ReconciliationNotFoundException):
            download_txt_file("non_existent_id")


def test_download_txt_file_not_finished(mock_reconciliation):
    mock_reconciliation.status = ReconciliationStatusEnum.ACTIVE
    mock_repository = Mock()
    mock_repository.get_reconciliation.return_value = mock_reconciliation

    with patch(
        "application.reconciliation_service.ReconciliationRepository",
        return_value=mock_repository,
    ):
        with pytest.raises(ReconciliationNotFinishedException):
            download_txt_file("rec123")


def test_download_txt_file_with_empty_transactions():
    empty_reconciliation = Reconciliation(
        id="rec123",
        cockpit_customer_id="cust123",
        customer_cnpj="**************",
        status=ReconciliationStatusEnum.FINISHED,
    )

    mock_repository = Mock()
    mock_repository.get_reconciliation.return_value = empty_reconciliation

    with patch(
        "application.reconciliation_service.ReconciliationRepository",
        return_value=mock_repository,
    ):
        result = download_txt_file("rec123")

        decoded_content = base64.b64decode(result).decode("utf-8")
        lines = decoded_content.split("\n")

        assert len(lines) == 1
        assert lines[0] == "|0000|**************|"
