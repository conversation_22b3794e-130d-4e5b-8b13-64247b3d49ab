from datetime import datetime, timedelta, timezone
from unittest.mock import Mock

import pytest
import responses
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from freezegun import freeze_time

from application.account_statement_service import AccountStatementService
from application.camunda_service import CamundaService
from application.draft_reconciliation_service import DraftReconciliationService
from domain.exceptions import (
    BankAccountGuidNotFoundException,
    ReconciliationFileUploadNotFoundException,
    StatementUploadException,
)
from domain.models import (
    BankAccountStatus,
    FileUploadStatus,
    Reconciliation,
    ReconciliationFileUpload,
    ReconciliationStatusEnum,
)
from infrastructure.cockpit_api_client import CockpitApiClient
from infrastructure.repositories.account_configuration_repository import (
    AccountConfigurationRepository,
)
from infrastructure.repositories.match_repository import MatchRepository
from infrastructure.repositories.partner_entity_repository import (
    PartnerEntityRepository,
)
from infrastructure.repositories.platform_transaction_repository import (
    PlatformTransactionRepository,
)
from infrastructure.repositories.reconciliation_repository import (
    ReconciliationRepository,
)
from infrastructure.repositories.upload_repository import UploadRepository
from infrastructure.upload_api_client import UploadApiClient
from shared.auth import Session


@pytest.fixture
def mock_upload_api():
    return Mock(spec=UploadApiClient)


@pytest.fixture
def service(mock_upload_api):
    session = Session(
        user_id="user_id",
        email="email",
        name="name",
        token="token",
    )
    return AccountStatementService(
        mock_upload_api,
        ReconciliationRepository(),
        UploadRepository(),
        MatchRepository(),
        DraftReconciliationService(
            cockpit_api_client=CockpitApiClient(),
            repository=ReconciliationRepository(),
            platform_transaction_repository=PlatformTransactionRepository(
                session=session,
                partner_repository=PartnerEntityRepository(),
                account_configuration_repository=AccountConfigurationRepository(),
            ),
            camunda_service=CamundaService(),
            session=session,
        ),
    )


@pytest.fixture
def test_reconciliation(db_test):
    reconciliation = Reconciliation(
        competence="2024-09",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["12345"],
        status_bank_account_guids={"12345": BankAccountStatus.EXTRACT_IMPORTED},
    )
    reconciliation.save()
    return reconciliation


@freeze_time("2025-01-01 12:00:00")
@pytest.mark.asyncio
async def test_upload_statement_success(
    service, mock_upload_api, db_test, test_reconciliation
):
    test_file_path = "test.pdf"
    test_bankaccount_guid = "12345"
    expected_response = {"status": "UPLOADED", "id": "upload-12345"}

    expected_service_return = {
        "latest_uploads_by_account": {
            "12345": {
                "error_message": None,
                "status": FileUploadStatus.UPLOADED,
                "upload_id": "upload-12345",
                "metadata": {
                    "upload_response": {"status": "UPLOADED", "id": "upload-12345"},
                    "upload_id": "upload-12345",
                    "status": "UPLOADED",
                    "uploaded_at": "2025-01-01T12:00:00",
                },
            }
        },
        "status_bank_account_guids": {"12345": "EXTRACT_IMPORTING"},
    }

    mock_upload_api.upload_file.return_value = expected_response

    result = await service.upload_statement(
        extension="pdf",
        file_path=test_file_path,
        bankaccount_guid=test_bankaccount_guid,
        reconciliation_id=str(test_reconciliation.id),
    )
    mock_upload_api.upload_file.assert_called_once_with(
        file_path=test_file_path,
        file_type="pdf",
        bankaccount_guid=test_bankaccount_guid,
        competence="2024-09",
    )
    assert result == expected_service_return


@pytest.mark.asyncio
async def test_upload_statement_api_error(
    service, mock_upload_api, db_test, test_reconciliation
):
    test_file_path = "test.pdf"
    error_response = {"error": "Invalid file format"}

    mock_upload_api.upload_file.side_effect = Exception(error_response)

    with pytest.raises(StatementUploadException):
        await service.upload_statement(
            file_path=test_file_path,
            extension="pdf",
            bankaccount_guid="12345",
            reconciliation_id=str(test_reconciliation.id),
        )

    mock_upload_api.upload_file.assert_called_once_with(
        file_path=test_file_path,
        file_type="pdf",
        bankaccount_guid="12345",
        competence="2024-09",
    )


@pytest.mark.asyncio
async def test_upload_statement_no_guid(
    service, mock_upload_api, db_test, test_reconciliation
):
    test_file_path = "test.ofx"
    expected_response = {"status": "success", "id": "upload456"}

    with pytest.raises(BankAccountGuidNotFoundException):
        mock_upload_api.upload_file.return_value = expected_response

        await service.upload_statement(
            extension="pdf",
            file_path=test_file_path,
            bankaccount_guid=None,
            reconciliation_id=str(test_reconciliation.id),
        )

        mock_upload_api.upload_file.assert_called_once_with(
            file_path=test_file_path, file_type="pdf", bankaccount_guid=None
        )


@pytest.mark.asyncio
async def test_update_statement_success(
    service, mock_upload_api, db_test, test_reconciliation
):
    file_upload = ReconciliationFileUpload(
        upload_id="upload-12345",
        reconciliation_id=str(test_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.UPLOADED,
    )
    file_upload.save()

    expected_response = {"invalid_data": []}
    expected_service_return = {
        "latest_uploads_by_account": {
            "12345": {
                "error_message": None,
                "status": FileUploadStatus.PROCESSING,
                "upload_id": "upload-12345",
                "metadata": None,
            }
        },
        "status_bank_account_guids": {"12345": "EXTRACT_PROCESSING"},
    }

    mock_upload_api.validate_upload.return_value = expected_response
    mock_upload_api.process_upload.return_value = {"transaction_batch_id": "id"}

    result = await service.update_statement(
        file_id="upload-12345",
    )

    mock_upload_api.validate_upload.assert_called_once_with(
        file_id="upload-12345",
        competence="2024-09",
    )
    mock_upload_api.process_upload.assert_called_once_with(file_id="upload-12345")

    assert result == expected_service_return


@pytest.mark.asyncio
async def test_update_statement_api_error(
    service, mock_upload_api, db_test, test_reconciliation
):
    file_upload = ReconciliationFileUpload(
        upload_id="upload-12345",
        reconciliation_id=str(test_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.UPLOADED,
    )
    file_upload.save()

    error_response = {"error": "Server error"}

    mock_upload_api.validate_upload.side_effect = Exception(error_response)

    with pytest.raises(Exception):
        await service.update_statement(
            file_id="upload-12345",
        )

    mock_upload_api.validate_upload.assert_called_once_with(
        file_id="upload-12345",
        competence="2024-09",
    )


@pytest.mark.asyncio
async def test_update_statement_error(
    service, mock_upload_api, db_test, test_reconciliation
):
    file_upload = ReconciliationFileUpload(
        upload_id="upload-12345",
        reconciliation_id=str(test_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.UPLOADED,
    )
    file_upload.save()

    with pytest.raises(ReconciliationFileUploadNotFoundException):
        await service.update_statement(
            file_id="not-found",
        )


@pytest.mark.asyncio
async def test_get_statement_pending_suggestions(service, mock_upload_api, db_test):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["12345"],
        status_bank_account_guids={"12345": BankAccountStatus.PENDING_SUGGESTIONS},
    ).save()

    file_upload = ReconciliationFileUpload(
        upload_id="upload-12345",
        reconciliation_id=str(_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.SHEET_PROCESSED,
        processed_at=datetime.now(timezone.utc) - timedelta(minutes=15),
    )
    file_upload.save()

    await service.get_statement("upload-12345")

    updated_reconciliation = Reconciliation.filter_by(id=_reconciliation.id)
    updated_reconciliation = updated_reconciliation[0]

    assert (
        updated_reconciliation.status_bank_account_guids["12345"]
        == BankAccountStatus.EXTRACT_IMPORTED
    )
    updated_upload = ReconciliationFileUpload.filter_by(upload_id="upload-12345")
    updated_upload = updated_upload[0]
    assert updated_upload.status == FileUploadStatus.SHEET_PROCESSED


@responses.activate
@pytest.mark.asyncio
async def test_get_statement_finished_status(service, mock_upload_api, db_test):
    responses._add_from_file(
        file_path="tests/fixtures/get_statement_finished_transactions_api.yaml"
    )
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["12345"],
        status_bank_account_guids={"12345": BankAccountStatus.EXTRACT_IMPORTING},
    ).save()

    file_upload = ReconciliationFileUpload(
        upload_id="upload-12345",
        reconciliation_id=str(_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.UPLOADED,
    )
    file_upload.save()

    assert len(_reconciliation.journal_headers) == 0

    mock_response = {"status": "FINISHED", "id": "upload-12345"}
    mock_upload_api.get_account_statement_upload.return_value = mock_response

    await service.get_statement("upload-12345")

    updated_reconciliation = Reconciliation.filter_by(id=_reconciliation.id)
    updated_reconciliation = updated_reconciliation[0]
    assert (
        updated_reconciliation.status_bank_account_guids["12345"]
        == BankAccountStatus.PENDING_SUGGESTIONS
    )
    updated_upload = ReconciliationFileUpload.filter_by(upload_id="upload-12345")
    updated_upload = updated_upload[0]
    assert updated_upload.status == FileUploadStatus.SHEET_PROCESSED
    assert len(updated_reconciliation.journal_headers) == 3


@pytest.mark.asyncio
async def test_get_statement_ready_to_validate_status(
    service,
    mock_upload_api,
    db_test,
):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["12345"],
        status_bank_account_guids={"12345": BankAccountStatus.EXTRACT_IMPORTING},
    ).save()

    file_upload = ReconciliationFileUpload(
        upload_id="upload-12345",
        reconciliation_id=str(_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.UPLOADED,
    )
    file_upload.save()

    mock_response = {
        "status": "SHEET_READY_TO_VALIDATE",
        "id": "upload-12345",
        "sheets_url": "link",
    }
    mock_upload_api.get_account_statement_upload.return_value = mock_response

    await service.get_statement("upload-12345")

    updated_reconciliation = Reconciliation.filter_by(id=_reconciliation.id)
    updated_reconciliation = updated_reconciliation[0]
    assert (
        updated_reconciliation.status_bank_account_guids["12345"]
        == BankAccountStatus.EXTRACT_IMPORTING
    )
    updated_upload = ReconciliationFileUpload.filter_by(upload_id="upload-12345")
    updated_upload = updated_upload[0]
    assert updated_upload.status == FileUploadStatus.SHEET_READY_TO_VALIDATE
    assert updated_upload._metadata["sheets_url"] == "link"


@pytest.mark.asyncio
async def test_get_statement_error_status(service, mock_upload_api, db_test):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["12345"],
        status_bank_account_guids={"12345": BankAccountStatus.EXTRACT_IMPORTING},
    ).save()

    file_upload = ReconciliationFileUpload(
        upload_id="upload-12345",
        reconciliation_id=str(_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.UPLOADED,
    )
    file_upload.save()

    mock_response = {
        "status": "ERROR",
        "id": "upload-12345",
        "error": "Processing failed",
    }
    mock_upload_api.get_account_statement_upload.return_value = mock_response

    await service.get_statement("upload-12345")

    updated_upload = ReconciliationFileUpload.filter_by(upload_id="upload-12345")
    assert updated_upload[0].status == FileUploadStatus.FAILED
    assert "Processing failed" in updated_upload[0].error_message


@pytest.mark.asyncio
async def test_get_statement_all_have_suggestions(
    service, mock_upload_api, db_test, mocker
):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["12345"],
        status_bank_account_guids={"12345": BankAccountStatus.PENDING_SUGGESTIONS},
    ).save()

    file_upload = ReconciliationFileUpload(
        upload_id="upload-12345",
        reconciliation_id=str(_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.SHEET_PROCESSED,
        processed_at=datetime.now(timezone.utc) - timedelta(minutes=5),
    )
    file_upload.save()

    mocker.patch.object(
        service.reconciliation_repository,
        "check_all_journal_headers_have_suggestions",
        return_value=True,
    )

    await service.get_statement("upload-12345")

    updated_reconciliation = Reconciliation.get(id=_reconciliation.id)
    assert (
        updated_reconciliation.status_bank_account_guids["12345"]
        == BankAccountStatus.EXTRACT_IMPORTED
    )
    updated_upload = ReconciliationFileUpload.filter_by(upload_id="upload-12345")
    assert updated_upload[0].status == FileUploadStatus.SHEET_PROCESSED


@pytest.mark.asyncio
async def test_get_statement_extract_imported_returns_metadata(
    service, mock_upload_api, db_test
):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["12345"],
        status_bank_account_guids={"12345": BankAccountStatus.EXTRACT_IMPORTED},
    ).save()

    file_upload = ReconciliationFileUpload(
        upload_id="upload-123456",
        reconciliation_id=str(_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.SHEET_PROCESSED,
    )
    file_upload.save()

    result = await service.get_statement("upload-123456")

    print(result)

    assert result == {
        "latest_uploads_by_account": {
            "12345": {
                "error_message": None,
                "metadata": None,
                "upload_id": "upload-123456",
                "status": FileUploadStatus.SHEET_PROCESSED,
            }
        },
        "status_bank_account_guids": {"12345": BankAccountStatus.EXTRACT_IMPORTED},
    }
    mock_upload_api.get_account_statement_upload.assert_not_called()


@pytest.mark.asyncio
async def test_handle_error_status(
    service, mock_upload_api, db_test, test_reconciliation
):
    file_upload = ReconciliationFileUpload(
        upload_id="upload-error-test",
        reconciliation_id=str(test_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.UPLOADED,
    )
    file_upload.save()

    bank_status = {"12345": BankAccountStatus.EXTRACT_IMPORTING}
    error_message = "Test error message"

    service._AccountStatementService__handle_error_status(
        latest_uploaded_file=file_upload,
        reconciliation=test_reconciliation,
        file_upload_status=FileUploadStatus.FAILED,
        bank_status=bank_status,
        error_message=error_message,
    )

    updated_reconciliation = Reconciliation.get(id=test_reconciliation.id)
    assert (
        updated_reconciliation.status_bank_account_guids["12345"]
        == bank_status["12345"]
    )

    updated_upload = ReconciliationFileUpload.get(id=file_upload.id)
    assert updated_upload.status == FileUploadStatus.FAILED
    assert updated_upload.error_message == error_message
    assert updated_upload.processed_at is None


@pytest.mark.asyncio
async def test_handler_file_upload_rules_http_exception(
    service, mock_upload_api, db_test, mocker
):
    _reconciliation = Reconciliation(
        competence="2024-10",
        status=ReconciliationStatusEnum.DRAFT,
        cockpit_customer_id="679",
        operator="Operator B",
        bank_account_guids=["12345"],
        status_bank_account_guids={"12345": BankAccountStatus.EXTRACT_IMPORTING},
    ).save()

    file_upload = ReconciliationFileUpload(
        upload_id="upload-12345",
        reconciliation_id=str(_reconciliation.id),
        cockpit_account_id="12345",
        file_type="pdf",
        status=FileUploadStatus.UPLOADED,
    )
    file_upload.save()

    mock_http_exception = HTTPException(
        status_code=status.HTTP_404_NOT_FOUND, detail="Upload not found"
    )
    mock_http_exception.status = status.HTTP_404_NOT_FOUND
    mock_upload_api.get_account_statement_upload.side_effect = mock_http_exception
    mock_handle_error = mocker.patch.object(
        service,
        "_AccountStatementService__handle_error_status",
        wraps=service._AccountStatementService__handle_error_status,
    )

    with pytest.raises(HTTPException) as exc_info:
        await service.get_statement("upload-12345")

    assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    mock_handle_error.assert_called_once()
    call_args = mock_handle_error.call_args[1]

    assert call_args["latest_uploaded_file"].upload_id == "upload-12345"
    assert call_args["reconciliation"].id == _reconciliation.id
    assert call_args["file_upload_status"] == FileUploadStatus.FAILED
    assert (
        call_args["bank_status"]["12345"] == BankAccountStatus.EXTRACT_PROCESSING_ERROR
    )
    assert "Upload not found" in call_args["error_message"]

    updated_reconciliation = Reconciliation.get(id=_reconciliation.id)
    assert (
        updated_reconciliation.status_bank_account_guids["12345"]
        == BankAccountStatus.EXTRACT_PROCESSING_ERROR
    )

    updated_upload = ReconciliationFileUpload.get(id=file_upload.id)
    assert updated_upload.status == FileUploadStatus.FAILED
    assert "Upload not found" in updated_upload.error_message
