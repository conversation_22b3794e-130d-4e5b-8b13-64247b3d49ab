# Métodos de Similaridade para Correspondência de Nomes

Este documento descreve os principais conceitos, funcionamento e diferenças entre três abordagens de similaridade utilizadas para comparar nomes de clientes/fornecedores: **Fuzzy Matching**, **TF-IDF (sklearn)** e **BERT**.

---

## 1. Fuzzy Matching (**FuzzyWuzzy**)

### Conceito
Fuzzy Matching é uma técnica baseada em comparação textual, que mede o quão semelhantes são duas strings, mesmo que não sejam idênticas. Utiliza algoritmos como Levenshtein Distance para calcular o número mínimo de operações necessárias para transformar uma string em outra.

### Funcionamento
- **Tokenização e Normalização:** As strings são normalizadas (removendo acentos, caixa, caracteres especiais) e podem ser divididas em tokens (palavras).
- **Cálculo de Similaridade:** Utiliza funções como `fuzz.token_set_ratio` (da biblioteca FuzzyWuzzy), que compara conjuntos de tokens, ignorando ordem e duplicidade.
- **Score:** O resultado é um score de 0 a 100, onde 100 indica igualdade total.

### Pontos Fortes
- Simples, rápido e eficiente para pequenas variações textuais.
- Lida bem com ordem trocada e pequenas diferenças.

### Limitações
- Não entende o significado das palavras (semântica).
- Sensível a abreviações e nomes muito diferentes.

---

## 2. TF-IDF + Cosine Similarity (sklearn)

### Conceito
TF-IDF (Term Frequency-Inverse Document Frequency) é uma técnica de representação vetorial de textos, que atribui pesos às palavras conforme sua frequência no documento e no corpus. A similaridade é medida pelo cosseno entre os vetores.

### Funcionamento
- **Vetorizar Nomes:** Cada nome é transformado em um vetor TF-IDF, representando a importância de cada termo.
- **Comparação:** Calcula-se a similaridade de cosseno entre os vetores dos nomes.
- **Score:** O resultado varia de 0 (sem similaridade) a 1 (idênticos).

### Pontos Fortes
- Considera a frequência e relevância dos termos.
- Rápido e escalável para grandes volumes de dados.

### Limitações
- Baseado apenas em texto, não entende semântica.
- Pequenas variações ou abreviações podem reduzir o score.

---

## 3. BERT (Embeddings Semânticos)

### Conceito
BERT (Bidirectional Encoder Representations from Transformers) é um modelo de linguagem profunda que gera embeddings (vetores) que capturam o significado semântico das frases. Permite comparar textos com base em significado, não apenas em similaridade textual.

### Funcionamento
- **Embeddings:** Cada nome é convertido em um vetor denso usando um modelo BERT/SBERT.
- **Comparação:** Calcula-se a similaridade de cosseno entre os embeddings dos nomes.
- **Score:** Varia de -1 a 1, sendo valores próximos de 1 altamente similares.

### Pontos Fortes
- Capta o significado das palavras e frases.
- Robusto a abreviações, ordem trocada e variações textuais.

### Limitações
- Mais pesado computacionalmente.
- Requer modelo pré-treinado e dependências adicionais.

---

## Resumo Comparativo

| Método         | Base         | Robustez a Variações | Capacidade Semântica | Performance |
|----------------|--------------|----------------------|----------------------|-------------|
| Fuzzy Matching | Textual      | Média                | Não                  | Alta        |
| TF-IDF         | Textual      | Baixa                | Não                  | Muito Alta  |
| BERT           | Semântica    | Alta                 | Sim                  | Média/Baixa |

---

**Recomendações de Uso:**
- Use **Fuzzy** para casos simples e pequenas variações.
- Use **TF-IDF** para grandes volumes e quando similaridade textual basta.
- Use **BERT** quando há muita variação, abreviação ou necessidade de captar o significado real dos nomes. 