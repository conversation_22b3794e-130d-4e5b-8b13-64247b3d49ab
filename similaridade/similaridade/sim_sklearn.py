from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from application.sim_fuzzy import clean_name

# Vetorizador global para reuso
_tfidf_vectorizer = TfidfVectorizer(analyzer="word", ngram_range=(1, 3))
_nome_tfidf_matrix = None
_nome_list = []
_nome2vec = {}


def normaliza_nome_tfidf(nome: str) -> str:
    return clean_name(nome)


def prepara_tfidf_nomes(nomes: list):
    global _nome_tfidf_matrix, _nome_list, _nome2vec
    _nome_list = [normaliza_nome_tfidf(n) for n in nomes]
    _nome_tfidf_matrix = _tfidf_vectorizer.fit_transform(_nome_list)
    _nome2vec = {n: _nome_tfidf_matrix[i] for i, n in enumerate(_nome_list)}


def name_similarity_sklearn(nome1: str, nome2: str) -> float:
    n1 = normaliza_nome_tfidf(nome1)
    n2 = normaliza_nome_tfidf(nome2)
    if not n1 or not n2:
        return 0.0
    if n1 == n2:
        return 1.0
    v1 = _nome2vec.get(n1)
    v2 = _nome2vec.get(n2)
    if v1 is None or v2 is None:
        return 0.0
    sim = cosine_similarity(v1, v2)[0, 0]
    return float(sim)
