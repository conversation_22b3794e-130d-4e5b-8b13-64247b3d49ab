import unidecode
import numpy as np

try:
    from transformers import AutoTokenizer, AutoModel
    import torch

    _bertimbau_model_name = "neuralmind/bert-base-portuguese-cased"
    _tokenizer = AutoTokenizer.from_pretrained(_bertimbau_model_name)
    _model = AutoModel.from_pretrained(_bertimbau_model_name)
    _nome2emb = {}

    def bertimbau_encode(text):
        inputs = _tokenizer(
            text, return_tensors="pt", truncation=True, padding=True, max_length=32
        )
        with torch.no_grad():
            outputs = _model(**inputs)
            # Usar a média dos embeddings da última camada
            emb = outputs.last_hidden_state.mean(dim=1).squeeze()
        return emb

    def prepara_bert_nomes(nomes: list):
        global _nome2emb
        _nome2emb = {
            unidecode.unidecode(n.lower().strip()): bertimbau_encode(n) for n in nomes
        }

    def name_similarity_bert(nome1: str, nome2: str) -> float:
        n1 = unidecode.unidecode(nome1.lower().strip())
        n2 = unidecode.unidecode(nome2.lower().strip())

        if not n1 or not n2:
            return 0.0

        emb1 = _nome2emb.get(n1)
        emb2 = _nome2emb.get(n2)

        if emb1 is None or emb2 is None:
            return 0.0

        emb1 = emb1.cpu().numpy()
        emb2 = emb2.cpu().numpy()

        score = float(
            np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
        )
        return score

except Exception:
    try:
        from sentence_transformers import SentenceTransformer

        _bert_model = SentenceTransformer("paraphrase-MiniLM-L6-v2")
        _nome2emb = {}

        def prepara_bert_nomes(nomes: list):
            global _nome2emb
            _nome2emb = {
                unidecode.unidecode(n.lower().strip()): _bert_model.encode(
                    n, convert_to_tensor=True
                )
                for n in nomes
            }

        def name_similarity_bert(nome1: str, nome2: str) -> float:
            n1 = unidecode.unidecode(nome1.lower().strip())
            n2 = unidecode.unidecode(nome2.lower().strip())

            if not n1 or not n2:
                return 0.0

            emb1 = _nome2emb.get(n1)
            emb2 = _nome2emb.get(n2)

            if emb1 is None or emb2 is None:
                return 0.0

            emb1 = emb1.cpu().numpy()
            emb2 = emb2.cpu().numpy()

            score = float(
                np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
            )
            return score

    except ImportError:

        def prepara_bert_nomes(nomes: list):
            pass

        def name_similarity_bert(nome1: str, nome2: str) -> float:
            return 0.0
