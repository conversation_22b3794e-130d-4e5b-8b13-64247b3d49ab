from typing import List

import requests
import json
import httpx

from invoice_suggestion import sugerir_notas_para_transacoes

TAKER_DOCUMENT = "42283979000100"
ISSUER_DOCUMENT = "42283979000100"
AUTH_CONCILIADOR = "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImppOGZnbllyQjZ4MDNOWnFCWU1UeCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.R_UD68o-rCUvyfDiqJKzoNM-kkkPLgNa7xlEwvxuW3b2szlNiOocXqvM6Leflfy20nEPssL8dnz9RvuT4ztJ562mBzgI0H8BI9_vlzPZmLGWwG-IXvR6fsRiC7kQniiMYJScguSVU3bUNGZ90wXozYi2KOKxLvQrr9jlyhY6odxWnB8ATWhplWPWjnf0WAI0s9hihYGxK8s5ZkcbEnjT1ZP6vZMr4abjCzJwPDiGZ62doLhLaazkoNoSBizQ_W_8744ymFyl5ioyUTd9KCk42Szb2O-7MwyMNnjLcAgj6ebbb5KMKOiIn6N2oTHfEPQDzbe-tuG0KfrYsX_NLg8Yfw"
AUTH_INTEGRADOR = "Bearer ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"


def request_transactions():
    url = "https://conciliador-api.bhub.ai/reconciliation/draft"

    payload = json.dumps(
        {
            "cockpit_customer_id": "6b743d9b-6b6d-408c-9184-dab574eeab42",
            "competence": "2025-05",
            "bank_account_guids": [
                # "3ffd3321-e07a-11ee-ba2b-0a7ea7812cad",
                # "40145d2b-e07a-11ee-ba2b-0a7ea7812cad",
                # "4029d960-e07a-11ee-ba2b-0a7ea7812cad",
                # "********-e07a-11ee-ba2b-0a7ea7812cad",
                "40574e06-e07a-11ee-ba2b-0a7ea7812cad",
                # "406d9ca2-e07a-11ee-ba2b-0a7ea7812cad",
                # "6770efb4-9971-4041-b6d8-2da9fbd44018",
                # "72aeeff8-6304-4c27-b454-062c9dc83c2a",
                # "7426ff7c-4728-46a2-a4bd-4866252ce468",
                # "c649d79c-22f0-47fd-995c-c3a67050a0f0"
            ],
        }
    )
    headers = {"Authorization": AUTH_CONCILIADOR, "Content-Type": "application/json"}

    response = requests.request("POST", url, headers=headers, data=payload)
    return response.json()


def request_invoices():
    offset = 0

    url = "https://integradornf.bhub.ai/api/v1/invoices"
    headers = {"Authorization": AUTH_INTEGRADOR}

    with httpx.Client() as client:
        while True:
            params = {
                "taker_document": TAKER_DOCUMENT,  # tomador da nota, emissor é o issuer_document (que é a bhub)
                "issuer_document": TAKER_DOCUMENT,
                "start_date": "2025-05-01",
                "end_date": "2025-05-31",
                "offset": str(offset),
                # "type": "NFSE",
                # testar sem especificar o tipo pra ver se retorna todos os tipos de notas, a nota fiscal eletronica não está sendo retornada
            }
            response = client.get(url, headers=headers, params=params)
            response.raise_for_status()
            response_json = response.json()
            data = response_json.get("data", [])

            for invoice in data:
                yield invoice

            next_page = response_json.get("pager", {}).get("nextOffset")

            if not data or next_page is None:
                break

            offset = next_page


def extract_transactions(dados: dict) -> List[dict]:
    return dados.get("transactions", [])


def extract_invoice_amount(nota: dict) -> float:
    net = nota.get("netAmount")
    if net is not None:
        try:
            return float(net)
        except Exception:
            pass
    amt = nota.get("amount")
    if amt is not None:
        try:
            return float(amt)
        except Exception:
            pass
    return 0.0


def main():
    transacoes_raw = request_transactions()
    notas = request_invoices()
    transacoes = extract_transactions(transacoes_raw)

    resultados = sugerir_notas_para_transacoes(transacoes, notas)
    for resultado in resultados:
        t = resultado["transacao"]
        sugestoes = resultado["sugestoes"]
        print(
            f"\nTransação: {t.get('id')} | Valor: {t.get('amount')} | Desc: {t.get('description')}"
        )
        if not sugestoes:
            print("  Nenhuma nota sugerida.")
        else:
            for nota, score in sugestoes:
                print(
                    f"  Nota: {nota.get('id')} | Valor: {nota.get('amount')} | Nome: {nota.get('issuerName') or nota.get('takerName')} | Score: {score:.2f}"
                )


if __name__ == "__main__":
    main()
