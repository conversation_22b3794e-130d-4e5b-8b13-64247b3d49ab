import re
from typing import List, <PERSON>ple
import unidecode
from similaridade import sim_sklearn, sim_bert
from application import sim_fuzzy
from difflib import SequenceMatcher


SIMILARITY_NAME_MODE = "fuzzy"  # 'fuzzy', 'sklearn', 'bert'


def normalize_str(s: str) -> str:
    """
    Normalize a string for comparison:
    - Converts to lowercase.
    - Removes accents (using unidecode).
    - Strips leading/trailing whitespace.
    Used to standardize names and documents before similarity calculation.
    """
    if not s:
        return ""
    s = s.lower().strip()
    s = unidecode.unidecode(s)
    return s


def normalize_cnpj(cnpj: str) -> str:
    """
    Normalize a CNPJ/CPF:
    - Removes all non-numeric characters.
    - Strips leading zeros.
    Ensures different CNPJ/CPF formats are comparable.
    """
    if not cnpj:
        return ""
    cnpj = re.sub(r"\D", "", cnpj)
    return cnpj.lstrip("0")


def string_similarity(a: str, b: str) -> float:
    """
    Compute similarity between two normalized strings using SequenceMatcher.
    Returns 1.0 for identical strings, or a score from 0 to 1 based on similarity.
    Used for comparing documents (CNPJ/CPF) and other textual fields.
    """
    a_n = normalize_str(a)
    b_n = normalize_str(b)
    if a_n == b_n:
        return 1.0
    return SequenceMatcher(None, a_n, b_n).ratio()


def amount_similarity(a: float, b: float) -> float:
    """
    Compute similarity between two numeric values (financial amounts):
    - Uses absolute value to allow matching between debit/credit.
    - Score 1.0 for identical values, decreasing linearly as the relative difference increases.
    """
    if a == 0 or b == 0:
        return 0.0
    return 1 - abs(abs(a) - abs(b)) / max(abs(a), abs(b))


def name_similarity(name1: str, name2: str) -> float:
    """
    Main dispatcher for name similarity calculation.
    Allows switching between fuzzy, TF-IDF, or BERT based on global configuration.
    """

    if SIMILARITY_NAME_MODE == "sklearn":
        return sim_sklearn.name_similarity_sklearn(name1, name2)

    if SIMILARITY_NAME_MODE == "bert":
        return sim_bert.name_similarity_bert(name1, name2)

    return sim_fuzzy.name_similarity_fuzzy(name1, name2)


def extract_transaction_cnpj_name(transaction: dict) -> Tuple[str, str]:
    """
    Extracts the relevant CNPJ/CPF and name from a financial transaction.
    Looks for the most probable fields (supplier, trading_entity, etc.).
    Ensures reconciliation uses the correct data for matching.
    """
    cnpj = (
        (transaction.get("supplier", {}) or {}).get("cpf_cnpj")
        or (transaction.get("trading_entity", {}) or {}).get("cpf_cnpj")
        or transaction.get("supplier_id")
        or transaction.get("customer_cnpj")
        or ""
    )
    name = (
        (transaction.get("supplier", {}) or {}).get("name")
        or (transaction.get("trading_entity", {}) or {}).get("name")
        or (transaction.get("customer", {}) or {}).get("name")
        or ""
    )
    return str(cnpj), name


def extract_invoice_cnpj_name(invoice: dict) -> Tuple[str, str]:
    """
    Extracts the relevant CNPJ/CPF and name from an invoice.
    Looks for the most probable fields (issuerDocument, customerDocument, etc.).
    Ensures reconciliation uses the correct data for matching.
    """
    cnpj = (
        invoice.get("issuerDocument")
        or invoice.get("customerDocument")
        or invoice.get("takerDocument")
        or ""
    )
    name = (
        invoice.get("issuerName")
        or invoice.get("customerName")
        or invoice.get("takerName")
        or invoice.get("providerName")
        or ""
    )
    return str(cnpj), name


def extract_invoice_amount(invoice: dict) -> float:
    """
    Extracts the relevant amount from an invoice:
    - Prioritizes netAmount if available.
    - Uses gross amount (amount) as fallback.
    Essential for accurate financial reconciliation, considering discounts and withholdings.
    """
    net = invoice.get("netAmount")
    if net is not None:
        try:
            return float(net)
        except Exception:
            pass
    amt = invoice.get("amount")
    if amt is not None:
        try:
            return float(amt)
        except Exception:
            pass
    return 0.0


def calculate_score(transaction: dict, invoice: dict) -> float:
    """
    Calculates the similarity score between a transaction and an invoice:
    - Considers amount, name, and document.
    - Strongly penalizes if name and document do not match.
    - Only returns maximum score if both name and document are highly similar.
    - Prevents false positives in generic names or matches by CNPJ only.
    Returns a score from 0 to 1, reflecting the confidence in the match.
    """
    value_trans = float(transaction.get("amount", 0) or 0)
    value_invoice = extract_invoice_amount(invoice)
    score_value = amount_similarity(value_trans, value_invoice)
    cnpj_trans, name_trans = extract_transaction_cnpj_name(transaction)
    cnpj_invoice, name_invoice = extract_invoice_cnpj_name(invoice)
    cnpj_trans_n = normalize_cnpj(cnpj_trans)
    cnpj_invoice_n = normalize_cnpj(cnpj_invoice)
    score_name = name_similarity(name_trans, name_invoice)
    score_doc = string_similarity(cnpj_trans_n, cnpj_invoice_n)

    # Novo: se documento e valor batem perfeitamente, sugere independente do nome
    if score_doc >= 0.98 and abs(abs(value_trans) - abs(value_invoice)) < 1e-2:
        print(
            f"[DEBUG][FORCE MATCH DOC] score_name={score_name:.2f} score_doc={score_doc:.2f} score_value={score_value:.2f} trans={value_trans} invoice={value_invoice}"
        )
        return 0.98

    if score_name < 0.5 and score_doc < 0.8:
        return 0.0
    if score_doc < 0.8:
        score_name = min(score_name, 0.3)
    if abs(abs(value_trans) - abs(value_invoice)) < 1e-2:
        print(
            f"[DEBUG] score_name={score_name:.2f} score_doc={score_doc:.2f} score_value={score_value:.2f} trans={value_trans} invoice={value_invoice}"
        )
        if score_name >= 0.75 and score_doc >= 0.9:
            return 0.98
    if score_value < 0.7:
        return score_value * max(score_name, score_doc)
    if score_name < 0.5 and score_doc < 0.5:
        return 0.0
    if max(score_name, score_doc) < 0.5:
        return max(score_name, score_doc) * 0.5
    if score_name < 0.5:
        return score_name * 0.5
    return 0.7 * max(score_name, score_doc) + 0.3 * score_value


def suggest_invoices_for_transaction(
    transaction: dict, invoices: List[dict], top_n: int = 3
) -> List[Tuple[dict, float]]:
    """
    Suggests the best invoices for a financial transaction:
    - Calculates the similarity score for each invoice.
    - Returns the top N suggestions with score above the threshold.
    - Orders by descending score.
    Enables automated, transparent, and auditable reconciliation.
    """
    scored = [(invoice, calculate_score(transaction, invoice)) for invoice in invoices]
    scored = [x for x in scored if x[1] > 0.5]
    scored.sort(key=lambda x: x[1], reverse=True)
    return scored[:top_n]


def prepara_bert_para_listas(transacoes: list, notas):
    """
    Pré-calcula embeddings BERT para todos os nomes únicos presentes em transações e notas.
    Chame esta função antes de rodar comparações massivas usando BERT para máxima performance.
    """
    nomes_trans = [
        (t.get("supplier", {}) or {}).get("name")
        or (t.get("trading_entity", {}) or {}).get("name")
        or (t.get("customer", {}) or {}).get("name")
        or ""
        for t in transacoes
    ]
    nomes_notas = [
        n.get("issuerName")
        or n.get("customerName")
        or n.get("takerName")
        or n.get("providerName")
        or ""
        for n in notas
    ]
    nomes = set([n for n in nomes_trans + nomes_notas if n])
    if nomes:
        sim_bert.prepara_bert_nomes(list(nomes))


def sugerir_notas_para_transacoes(transacoes: list, notas, top_n: int = 3) -> list:
    """
    Para cada transação, sugere as melhores notas fiscais, encapsulando toda a lógica de preparação de embeddings e matching.
    Retorna uma lista de dicionários: {'transacao': <dict>, 'sugestoes': [(nota, score), ...]}
    """
    notas = list(notas)

    prepara_bert_para_listas(transacoes, notas)
    resultados = []
    for t in transacoes:
        sugestoes = suggest_invoices_for_transaction(t, notas, top_n=top_n)
        resultados.append({"transacao": t, "sugestoes": sugestoes})
    return resultados
