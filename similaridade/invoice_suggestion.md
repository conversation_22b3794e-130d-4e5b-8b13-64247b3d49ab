# Documentação das Regras de Matching — `invoice_suggestion.py`

## Visão Geral

O script `invoice_suggestion.py` implementa a lógica de sugestão automática de notas fiscais (invoices) para transações financeiras, utilizando múltiplos critérios de similaridade. O objetivo é automatizar e tornar transparente o processo de conciliação, maximizando a precisão e minimizando falsos positivos.

---

## Componentes-Chave

- **Normalização de Strings e Documentos:**
  - Funções para padronizar nomes e documentos (CNPJ/CPF), removendo acentos, espaços e caracteres especiais.
- **Cálculo de Similaridade:**
  - Similaridade de nomes: pode usar BERT, TF-IDF ou fuzzy matching.
  - Similaridade de documentos: compara CNPJ/CPF normalizados.
  - Similaridade de valores: compara valores absolutos, tolerando pequenas diferenças.
- **Função Central:**
  - `calculate_score(transaction, invoice)`: calcula o score de matching entre uma transação e uma nota fiscal.

---

## Regras e Pesos do Matching (`calculate_score`)

A função `calculate_score` avalia a compatibilidade entre uma transação e uma nota fiscal considerando **três dimensões principais**:

1. **Valor (amount)**
2. **Nome (name)**
3. **Documento (CNPJ/CPF)**

O score final é um valor entre 0 e 1, refletindo a confiança no match.

### 1. Similaridade de Valor (`score_value`)
- Calculada pela função `amount_similarity`.
- Mede a proximidade entre os valores absolutos da transação e da nota.
- Score máximo (1.0) se os valores são idênticos; decresce linearmente conforme a diferença aumenta.
- Se um dos valores for zero, retorna 0.0.

### 2. Similaridade de Nome (`score_name`)
- Calculada pela função `name_similarity`.
- Pode usar BERT, TF-IDF ou fuzzy matching (configurável).
- Mede a similaridade semântica ou textual entre os nomes extraídos da transação e da nota.

### 3. Similaridade de Documento (`score_doc`)
- Calculada pela função `string_similarity` após normalização dos documentos.
- Mede o quão próximos são os CNPJ/CPF das duas entidades.
- Score máximo (1.0) para documentos idênticos.

---

## Regras de Decisão e Pesos

A função `calculate_score` aplica as seguintes regras, em ordem de prioridade:

1. **Match Forçado por Documento e Valor:**
   - Se o documento (CNPJ/CPF) bate quase perfeitamente (`score_doc >= 0.98`) **e** o valor é praticamente igual (diferença menor que 0.01), retorna score 0.98, independentemente do nome.
   - Justificativa: documento e valor batendo é um forte indício de correspondência, mesmo que o nome varie.

2. **Descarta Matches Fracos:**
   - Se tanto o nome quanto o documento têm baixa similaridade (`score_name < 0.5` **e** `score_doc < 0.8`), retorna 0.0 (descarta o match).
   - Se o documento tem baixa similaridade (`score_doc < 0.8`), limita o score do nome a no máximo 0.3 (penalização).

3. **Match Forte por Nome e Documento com Valor Igual:**
   - Se o valor é praticamente igual **e** nome e documento são ambos altos (`score_name >= 0.75` **e** `score_doc >= 0.9`), retorna 0.98.

4. **Penalização por Valor Muito Diferente:**
   - Se a similaridade de valor for baixa (`score_value < 0.7`), retorna `score_value * max(score_name, score_doc)`.

5. **Descarta Matches Muito Fracos:**
   - Se ambos nome e documento são muito baixos (`score_name < 0.5` **e** `score_doc < 0.5`), retorna 0.0.
   - Se o maior entre nome e documento for menor que 0.5, retorna metade desse valor.
   - Se só o nome for baixo, retorna metade do score do nome.

6. **Score Combinado (Regra Geral):**
   - Nos demais casos, retorna `0.7 * max(score_name, score_doc) + 0.3 * score_value`.
   - Ou seja, **nome/documento têm peso de 70%** e **valor tem peso de 30%** no score final.

---

## Resumo das Relevâncias

- **Documento (CNPJ/CPF):**
  - Tem prioridade máxima: se bater quase perfeitamente com valor igual, força o match.
  - Se for baixo, penaliza fortemente o score do nome.
- **Nome:**
  - Tem peso alto (até 70% do score final).
  - Se for muito baixo, pode descartar o match ou penalizar o score.
- **Valor:**
  - Tem peso de 30% no score final.
  - Se for muito diferente, pode descartar o match ou reduzir o score drasticamente.

---

## Estratégia de Matching

- O sistema é **conservador**: exige alta similaridade em pelo menos dois dos três critérios para sugerir um match forte.
- **Evita falsos positivos** em casos de nomes genéricos ou matches apenas por documento.
- Permite matches mesmo com pequenas variações de nome, desde que documento e valor batam.

---

## Fluxo de Uso

1. **Pré-processamento:**
   - Normalização de nomes e documentos.
   - Pré-cálculo de embeddings BERT para performance.
2. **Matching:**
   - Para cada transação, calcula o score para cada nota fiscal.
   - Retorna as melhores sugestões (acima de um threshold, tipicamente 0.5).

---

## Observações

- O modo de similaridade de nome pode ser ajustado (`SIMILARITY_NAME_MODE`), permitindo experimentação entre métodos semânticos e textuais.
- O sistema é extensível para outros critérios, bastando ajustar a função `calculate_score`. 