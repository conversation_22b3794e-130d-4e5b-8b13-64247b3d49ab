import pytest
from invoice_suggestion import suggest_invoices_for_transaction
import invoice_suggestion as snpt

snpt.SIMILARITY_NAME_MODE = "fuzzy"


def make_transaction(amount, name, cnpj):
    return {
        "amount": amount,
        "trading_entity": {"name": name, "cpf_cnpj": cnpj},
        "description": name,
    }


def make_invoice(amount, name, cnpj, netAmount=None):
    invoice = {
        "amount": str(amount),
        "issuerName": name,
        "issuerDocument": cnpj,
    }
    if netAmount is not None:
        invoice["netAmount"] = str(netAmount)
    return invoice


def test_perfect_match():
    trans = make_transaction(-100.0, "EMPRESA X LTDA", "12345678000199")
    invoice = make_invoice(100.0, "EMPRESA X LTDA", "12.345.678/0001-99")
    suggestions = suggest_invoices_for_transaction(trans, [invoice], top_n=1)
    assert suggestions, "Should suggest invoice"
    invoice_suggested, score = suggestions[0]
    assert score > 0.95


def test_similar_name_equal_amount():
    trans = make_transaction(-200.0, "EMPRESA Y LTDA", "11111111000111")
    invoice = make_invoice(200.0, "EMPRESA Y LTDA.", "11111111000111")
    suggestions = suggest_invoices_for_transaction(trans, [invoice], top_n=1)
    assert suggestions
    assert suggestions[0][1] > 0.9


def test_name_with_numeric_prefix():
    trans = make_transaction(-300.0, "MARIA DA SILVA", "22222222000122")
    invoice = make_invoice(300.0, "22.222.222 MARIA DA SILVA", "22222222000122")
    suggestions = suggest_invoices_for_transaction(trans, [invoice], top_n=1)
    assert suggestions[0][1] > 0.9


def test_different_cnpj():
    trans = make_transaction(-400.0, "EMPRESA Z", "33333333000133")
    invoice = make_invoice(400.0, "EMPRESA Z", "99999999000199")
    suggestions = suggest_invoices_for_transaction(trans, [invoice], top_n=1)
    assert not suggestions or suggestions[0][1] < 0.95


def test_different_name():
    trans = make_transaction(-500.0, "EMPRESA A", "44444444000144")
    invoice = make_invoice(500.0, "EMPRESA B", "44444444000144")
    suggestions = suggest_invoices_for_transaction(trans, [invoice], top_n=1)
    assert not suggestions or suggestions[0][1] < 0.5


def test_different_amount_same_name():
    trans = make_transaction(-600.0, "EMPRESA C", "55555555000155")
    invoice = make_invoice(1000.0, "EMPRESA C", "55555555000155")
    suggestions = suggest_invoices_for_transaction(trans, [invoice], top_n=1)
    assert suggestions[0][1] < 0.7


def test_net_amount():
    trans = make_transaction(-700.0, "EMPRESA D", "66666666000166")
    invoice = make_invoice(800.0, "EMPRESA D", "66666666000166", netAmount=700.0)
    suggestions = suggest_invoices_for_transaction(trans, [invoice], top_n=1)
    assert suggestions[0][1] > 0.9


def test_person_name_vs_company():
    trans = make_transaction(-800.0, "JOAO DA SILVA", "77777777000177")
    invoice = make_invoice(800.0, "EMPRESA EIRELI", "77777777000177")
    suggestions = suggest_invoices_for_transaction(trans, [invoice], top_n=1)
    assert not suggestions or suggestions[0][1] < 0.5


def test_missing_fields():
    trans = {"amount": -900.0, "description": "EMPRESA F"}
    invoice = {"amount": "900.0", "issuerName": "EMPRESA F"}
    suggestions = suggest_invoices_for_transaction(trans, [invoice], top_n=1)
    assert suggestions[0][1] > 0.7


def test_negative_positive_amount():
    trans = make_transaction(-1000.0, "EMPRESA G", "88888888000188")
    invoice = make_invoice(1000.0, "EMPRESA G", "88888888000188")
    suggestions = suggest_invoices_for_transaction(trans, [invoice], top_n=1)
    assert suggestions[0][1] > 0.9
