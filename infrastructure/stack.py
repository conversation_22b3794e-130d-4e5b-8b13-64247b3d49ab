import aws_cdk as cdk
from bhub_cdk.common import BusinessUnit
from bhub_cdk.stack import ApplicationStack as BHubApplicationStack
from constructs import Construct

from infrastructure.application import ApplicationStack

SERVICE_NAME = "conciliador-back"


class ConciliadorBackStack(BHubApplicationStack):
    @property
    def BUSINESS_UNIT(self) -> str:
        return BusinessUnit.CAAS

    @property
    def SERVICE_NAME(self) -> str:
        return SERVICE_NAME

    def __init__(self, scope: Construct, id: str, **kwargs) -> None:
        super().__init__(scope, id, **kwargs)

        app_env = cdk.CfnParameter(self, "AppEnv", no_echo=True)
        encrypt_key = cdk.CfnParameter(self, "EncryptKey", no_echo=True)

        match_bucket = cdk.CfnParameter(self, "MatchBucket", no_echo=True)
        account_bucket = cdk.CfnParameter(self, "AccountBucket", no_echo=True)
        camunda_endpoint = cdk.CfnParameter(self, "CamundaEndpoint", no_echo=True)
        camunda_username = cdk.CfnParameter(self, "CamundaUsername", no_echo=True)
        camunda_password = cdk.CfnParameter(self, "CamundaPassword", no_echo=True)
        camunda_process_key = cdk.CfnParameter(self, "CamundaProcessKey", no_echo=True)
        transactions_api_base_url = cdk.CfnParameter(
            self, "TransactionsApiBaseUrl", no_echo=True
        )
        cockpit_api_base_url = cdk.CfnParameter(self, "CockpitApiBaseUrl", no_echo=True)
        upload_api_base_url = cdk.CfnParameter(self, "UploadApiBaseUrl", no_echo=True)
        feature_flag_simplified_categorizer = cdk.CfnParameter(
            self, "FeatureFlagSimplifiedCategorizer", no_echo=True
        )
        binder_arn = cdk.CfnParameter(self, "BinderArn", no_echo=True)

        environment = {
            "APP_ENV": app_env.value_as_string,
            "ENCRYPT_KEY": encrypt_key.value_as_string,
            "MATCH_BUCKET": match_bucket.value_as_string,
            "CAMUNDA_ENDPOINT": camunda_endpoint.value_as_string,
            "CAMUNDA_USERNAME": camunda_username.value_as_string,
            "CAMUNDA_PASSWORD": camunda_password.value_as_string,
            "CAMUNDA_PROCESS_KEY": camunda_process_key.value_as_string,
            "TRANSACTIONS_API_BASE_URL": transactions_api_base_url.value_as_string,
            "COCKPIT_API_BASEURL": cockpit_api_base_url.value_as_string,
            "ACCOUNT_BUCKET": account_bucket.value_as_string,
            "FEATURE_FLAG_SIMPLIFIED_CATEGORIZER": feature_flag_simplified_categorizer.value_as_string,  # noqa
            "BINDER_ARN": binder_arn.value_as_string,
            "UPLOAD_API_BASE_URL": upload_api_base_url.value_as_string,
        }

        self.application_stack = ApplicationStack(
            self,
            "ConciliadorBackApplicationStack",
            environment=environment,
        )

        self.termination_protection = True
