import re
from functools import lru_cache

from domain.models import SuggestionTypeEnum
from domain.repositories import AbstractMatchRepository
from infrastructure.invoices_api_client import InvoicesApiClient
from infrastructure.storing.abstract_storing import AbstractStoring
from shared.logger import get_logger

logger = get_logger()


class ImportMatchRecords:
    def __init__(
        self,
        storing: AbstractStoring,
        repository: AbstractMatchRepository,
        invoices_api_client: InvoicesApiClient,
    ):
        self.storing = storing
        self.repository = repository
        self.invoices_api_client = invoices_api_client

    def import_match(self, match_path: str):  # noqa
        def extract_cnpj_from_path(input_string):
            cnpj_pattern = r"cnpj=(\d{14})"
            match = re.search(cnpj_pattern, input_string)
            if match:
                return match.group(1)
            return None

        def update_data(new, suggested_invoice):
            suggested_invoice.suggestion_id = new.suggestion_id
            suggested_invoice.cockpit_account_id = new.cockpit_account_id
            suggested_invoice.ranking = new.ranking
            suggested_invoice.exact_match = new.exact_match
            suggested_invoice.version = new.version
            suggested_invoice.should_not_be_matched = new.should_not_be_matched
            if new.suggestion_type == SuggestionTypeEnum.ACCOUNT:
                suggested_invoice.account_data = new.account_data
            return suggested_invoice

        def add_details(suggested_invoice, invoice):
            if (
                not invoice
                or suggested_invoice.suggestion_type != SuggestionTypeEnum.INVOICE
            ):
                return suggested_invoice

            cnpj = extract_cnpj_from_path(match_path)
            suggested_invoice.invoice_date = invoice.get("issueDate")
            suggested_invoice.invoice_company_name = (
                invoice.get("takerName")
                if invoice.get("issuerDocument") == cnpj
                else invoice.get("issuerName")
            )
            suggested_invoice.invoice_value = invoice.get("amount")
            suggested_invoice.invoice_number = invoice.get("number")
            return suggested_invoice

        logger.info(f"Getting object: {match_path}")

        suggested_invoices = self.storing.get_suggested_invoices(match_path)

        invoice_ids = list(
            set(
                [
                    invoice.platform_invoice_id
                    for invoice in suggested_invoices
                    if invoice.suggestion_type == SuggestionTypeEnum.INVOICE
                    and invoice.platform_invoice_id
                ]
            )
        )

        invoices = (
            self.invoices_api_client.get_invoices_by_ids(invoice_ids)
            if invoice_ids
            else []
        )

        search_invoice_ids = list(map(lambda x: x.get("id"), invoices))

        @lru_cache(maxsize=None)
        def get_invoice(platform_invoice_id):
            try:
                return invoices[search_invoice_ids.index(platform_invoice_id)]
            except ValueError:
                return None

        updated = []
        for item in suggested_invoices:
            existing = self.repository.get_by_platform_id(
                item.platform_transaction_id,
                item.platform_invoice_id,
                item.suggestion_type,
            )

            if item.suggestion_type == SuggestionTypeEnum.INVOICE:
                invoice = get_invoice(item.platform_invoice_id)
                if existing:
                    updated.append(add_details(update_data(item, existing), invoice))
                else:
                    updated.append(add_details(item, invoice))
            else:
                if existing:
                    updated.append(update_data(item, existing))
                else:
                    updated.append(item)

        self.repository.create_or_update(updated)
        get_invoice.cache_clear()
        return len(updated)
