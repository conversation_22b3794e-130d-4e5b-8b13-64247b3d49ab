from copy import deepcopy
from datetime import datetime
from typing import List, Union

from sqlalchemy import and_, not_, select

from api.mappers import JournalLineResponse
from domain.exceptions import (
    DebitCreditConstraintError,
    JournalHeaderNotFoundException,
    JournalLineBatchUpdateError,
    JournalOperationNotAllowedDueToReconciliationStatus,
    SameAmountConstraintError,
    TransactionMetadataUpdateError,
)
from domain.models import (
    InvoiceMotiveEnum,
    JournalHeader,
    JournalLine,
    LinkedInvoice,
    OperationTypeEnum,
    PartnerEntity,
    ReconciliationStatusEnum,
    TransactionStatusEnum,
    TransactionTypeEnum,
)
from infrastructure.repositories.journal_repository import JournalRepository
from shared.logger import get_logger
from shared.session_manager import session_scope
from shared.utils.journal_status import determine_journal_status

logger = get_logger()


def update_journals_account_data(bank_code: str, reconciliation_id: str):
    journal_repository = JournalRepository()

    return journal_repository.set_journal_account_based_on_chart_of_accounts(
        bank_code=bank_code,
        reconciliation_id=reconciliation_id,
    )


def __clear_linked_invoices_journal(journal_line_id):
    linked_invoices = LinkedInvoice().filter_by(
        journal_line_id=journal_line_id, deleted=False
    )
    invoices = []

    for invoice in linked_invoices:
        invoice.deleted = True
        invoice.deleted_at = datetime.now()
        invoices.append(invoice)

    LinkedInvoice().bulk_save_objects(invoices)


def change_journal_status(journal_header_id):
    journal_repository = JournalRepository()
    journal_header = journal_repository.get_journal_header(journal_header_id)

    new_status = determine_journal_status(journal_header)

    if new_status != journal_header.status:
        if new_status == TransactionStatusEnum.NEW:
            journal_repository.set_journal_status_to_new(journal_header)
        elif new_status == TransactionStatusEnum.RECONCILED:
            journal_repository.set_journal_status_to_reconciled(journal_header)

    return journal_header


def _validate_three_line_deletion(header, journal_line_id: str):
    """Validate if a line can be deleted when there are exactly 3 lines."""
    amount_sum = 0
    line_amounts = []

    for line in header.journal_lines:
        if str(line.id) != journal_line_id:
            signed_amount = line.amount
            if line.line_type == OperationTypeEnum.DEBIT:
                signed_amount = -line.amount

            amount_sum += signed_amount
            line_amounts.append(abs(line.amount))

    if amount_sum != 0 and line_amounts[0] != line_amounts[1] != header.amount:
        raise SameAmountConstraintError()


def _validate_debit_credit_balance(remaining_lines):
    """Ensure there's at least one debit and one credit line."""
    has_debit = any(
        line.line_type == OperationTypeEnum.DEBIT for line in remaining_lines
    )
    has_credit = any(
        line.line_type == OperationTypeEnum.CREDIT for line in remaining_lines
    )

    if not (has_debit and has_credit):
        raise DebitCreditConstraintError()


def delete_journal_line(journal_line_id: str):
    repository = JournalRepository()
    journal_line = repository.get_journal_line(journal_line_id)
    header = journal_line.journal_header

    if header.reconciliation.status != ReconciliationStatusEnum.ACTIVE:
        raise JournalOperationNotAllowedDueToReconciliationStatus(
            operation="delete", current_status=header.reconciliation.status
        )

    if len(header.journal_lines) == 3:
        _validate_three_line_deletion(header, journal_line_id)

    remaining_lines = [
        line
        for line in header.journal_lines
        if not line.deleted and line.id != journal_line.id
    ]

    _validate_debit_credit_balance(remaining_lines)

    repository.delete_journal_line(journal_line)
    change_journal_status(header.id)


def update_journal_line(
    journal_line_id: str, updated_data: dict
) -> JournalLineResponse:
    """Updates a journal line with the provided data."""
    journal_line = JournalRepository().get_journal_line(journal_line_id)

    __validate_journal_line_update(journal_line)

    invoice_data = __extract_invoice_data(updated_data)

    __update_line_account(updated_data)

    metadata = __handle_invoice_updates(journal_line, invoice_data)

    for field, value in updated_data.items():
        setattr(journal_line, field, value)

    setattr(journal_line, "_metadata", metadata)

    journal_line.save(refresh=True)

    change_journal_status(journal_line.journal_header_id)

    return JournalLineResponse.build(journal_line)


def __validate_journal_line_update(journal_line: JournalLine):
    """Validates if the journal line can be updated based on reconciliation status."""
    header = journal_line.journal_header
    if header.reconciliation.status != ReconciliationStatusEnum.ACTIVE:
        raise JournalOperationNotAllowedDueToReconciliationStatus(
            operation="update", current_status=header.reconciliation.status
        )


def __extract_invoice_data(updated_data: dict) -> dict:
    """Extracts and removes invoice-related data from the update dictionary."""
    invoice_data = {
        "has_linked_invoices": updated_data.pop("has_linked_invoices", None),
        "should_link_invoices": updated_data.pop("should_link_invoices", None),
        "linked_invoices": updated_data.pop("linked_invoices", None),
        "is_linked_invoice_applicable": updated_data.pop(
            "is_linked_invoice_applicable", None
        ),
        "invoice_motive": updated_data.pop("invoice_motive", None),
    }
    return invoice_data


def __update_line_account(updated_data):
    if "line_type" not in updated_data:
        return

    if updated_data["line_type"] == OperationTypeEnum.DEBIT:
        updated_data["account_id"] = updated_data.pop("expense_account")
    elif updated_data["line_type"] == OperationTypeEnum.CREDIT:
        updated_data["account_id"] = updated_data.pop("income_account")

    updated_data.pop("income_account", None)
    updated_data.pop("expense_account", None)


def __handle_invoice_updates(journal_line: JournalLine, invoice_data: dict):
    """Handles invoice metadata and linking updates."""
    is_linked_invoice_applicable = invoice_data["is_linked_invoice_applicable"]
    has_linked_invoices = invoice_data["has_linked_invoices"]
    invoice_motive = invoice_data["invoice_motive"]
    linked_invoices = invoice_data["linked_invoices"]

    if invoice_data["should_link_invoices"]:
        __handle_invoice_linking(journal_line, invoice_data["linked_invoices"])

    if has_linked_invoices is not None:
        return __update_invoice_metadata(
            journal_line=journal_line,
            has_linked_invoices=has_linked_invoices,
            is_linked_invoice_applicable=None,
            invoice_motive=None,
            linked_invoices=invoice_data["linked_invoices"],
        )

    # Update metadata if applicable
    if is_linked_invoice_applicable is not None:
        return __update_invoice_metadata(
            journal_line=journal_line,
            has_linked_invoices=None,
            is_linked_invoice_applicable=is_linked_invoice_applicable,
            invoice_motive=invoice_motive,
            linked_invoices=invoice_data["linked_invoices"],
        )

    if linked_invoices:
        return __update_invoice_metadata(
            journal_line=journal_line,
            has_linked_invoices=None,
            is_linked_invoice_applicable=None,
            invoice_motive=None,
            linked_invoices=invoice_data["linked_invoices"],
        )

    return journal_line._metadata


def __update_invoice_metadata(
    journal_line: JournalLine,
    has_linked_invoices: Union[bool, None],
    is_linked_invoice_applicable: Union[bool, None],
    invoice_motive: str,
    linked_invoices: List[LinkedInvoice],
):
    """Updates the invoice metadata for a journal line."""
    metadata = deepcopy(journal_line._metadata) or {}

    if is_linked_invoice_applicable is False:
        metadata["is_linked_invoice_applicable"] = is_linked_invoice_applicable
        metadata["has_linked_invoices"] = None
        metadata["invoice_motive"] = None

    if has_linked_invoices is False:
        metadata["has_linked_invoices"] = has_linked_invoices
        metadata["is_linked_invoice_applicable"] = None
        metadata["invoice_motive"] = None

    if invoice_motive is not None:
        try:
            motive_value = InvoiceMotiveEnum.from_display_value(invoice_motive)
            metadata["invoice_motive"] = motive_value
        except ValueError as e:
            raise TransactionMetadataUpdateError(
                f"Erro ao atualizar o motivo: {str(e)}"
            )

    if linked_invoices:
        metadata["has_linked_invoices"] = None
        metadata["is_linked_invoice_applicable"] = None
        metadata["invoice_motive"] = None

    return metadata


def __handle_invoice_linking(
    journal_line: JournalLine, linked_invoices_data: List[dict]
):
    """Handles the linking of invoices to a journal line."""
    __clear_linked_invoices_journal(journal_line_id=journal_line.id)

    if linked_invoices_data:
        invoices = [
            LinkedInvoice(
                platform_invoice_id=invoice["platform_invoice_id"],
                link_date=datetime.now(),
                link_percentual=0,
                invoice_number=invoice.get("invoice_number"),
                file_path=invoice.get("file_path"),
                journal_line_id=journal_line.id,
            )
            for invoice in linked_invoices_data
        ]
        LinkedInvoice().bulk_save_objects(invoices)


def create_journal_line(journal_header_id: str) -> JournalLine:
    journal_repository = JournalRepository()
    journal_header = journal_repository.get_journal_header(journal_header_id)

    if journal_header.reconciliation.status != ReconciliationStatusEnum.ACTIVE:
        raise JournalOperationNotAllowedDueToReconciliationStatus(
            operation="insert", current_status=journal_header.reconciliation.status
        )

    journal_line_instance = JournalLine(
        journal_header_id=journal_header_id,
        line_type=(
            OperationTypeEnum.CREDIT
            if journal_header.type == TransactionTypeEnum.EXPENSE
            else OperationTypeEnum.DEBIT
        ),
        amount=float(0.0),
        description=journal_header.description,
        _metadata={},
    )
    journal_line = journal_repository.insert_journal_line(journal_line_instance)

    journal_header = journal_line.journal_header

    journal_header.status = TransactionStatusEnum.NEW
    journal_header.save()

    return journal_line


async def bulk_update_journal_line_accounts_service(
    journal_line_ids: List[str], account_id: str
) -> List[dict]:
    """
    Service function to update account_id for multiple journal lines.
    Optimized for bulk operations on journal lines using efficient batch processing.

    Args:
        journal_line_ids: List of journal line IDs to update
        account_id: The account ID to set on all journal lines

    Returns:
        List of results with updated journal line IDs

    Raises:
        JournalLineBatchUpdateError: If any line cannot be updated
    """

    try:
        with session_scope(close=False) as session:
            stmt = select(JournalLine).filter(
                and_(JournalLine.id.in_(journal_line_ids), not_(JournalLine.deleted))
            )
            journal_lines = session.execute(stmt).unique().scalars().all()

            found_ids = {str(line.id) for line in journal_lines}
            missing_ids = set(journal_line_ids) - found_ids
            if missing_ids:
                raise JournalLineBatchUpdateError(
                    f"Journal lines not found: {', '.join(missing_ids)}"
                )

            header_ids = {line.journal_header_id for line in journal_lines}
            headers_stmt = select(JournalHeader).filter(
                JournalHeader.id.in_(header_ids)
            )
            headers = session.execute(headers_stmt).unique().scalars().all()
            headers_dict = {str(header.id): header for header in headers}

            invalid_headers = []
            for header_id, header in headers_dict.items():
                if header.reconciliation.status != ReconciliationStatusEnum.ACTIVE:
                    invalid_headers.append((header_id, header.reconciliation.status))

            if invalid_headers:
                invalid_details = [
                    f"{header_id} (Status: {status})"
                    for header_id, status in invalid_headers
                ]
                raise JournalLineBatchUpdateError(
                    f"Cannot update journal lines in headers: "
                    f"{', '.join(invalid_details)} "
                    f"because they are not in an ACTIVE reconciliation."
                )

            results = []
            for line in journal_lines:
                journal_header = line.journal_header

                line.account_id = account_id

                journal_header.status = determine_journal_status(journal_header)
                results.append(
                    {
                        "id": str(line.id),
                        "message": "Journal line account updated successfully",
                    }
                )

            chunk_size = 100
            for i in range(0, len(journal_lines), chunk_size):
                chunk = journal_lines[i : i + chunk_size]  # noqa
                session.bulk_save_objects(chunk)

            session.commit()

            return results

    except JournalLineBatchUpdateError:
        raise
    except Exception as e:
        logger.error(f"Error in bulk account update: {str(e)}")
        raise JournalLineBatchUpdateError(
            f"Error updating journal line accounts: {str(e)}"
        )


async def edit_journal_header(
    journal_header_id: str,
    updated_data: dict,
) -> JournalHeader:
    """
    Updates a journal header's basic information and partner entity data.
    """
    journal_repository = JournalRepository()
    journal_header = journal_repository.get_journal_header(journal_header_id)

    if not journal_header:
        raise JournalHeaderNotFoundException(journal_header_id)

    if journal_header.reconciliation.status != ReconciliationStatusEnum.ACTIVE:
        raise JournalOperationNotAllowedDueToReconciliationStatus(
            operation="update", current_status=journal_header.reconciliation.status
        )

    try:
        if (
            "partner_entity_cpf_cnpj" in updated_data
            or "partner_entity_name" in updated_data
        ):
            update_or_create_partner_entity_for_journal(
                cpf_cnpj=updated_data.get("partner_entity_cpf_cnpj"),
                name=updated_data.get("partner_entity_name"),
                journal_header=journal_header,
            )

        # Handle other fields
        allowed_fields = {"notes", "description", "amount", "date"}
        for field, value in updated_data.items():
            if field in allowed_fields and value is not None:
                setattr(journal_header, field, value)

        journal_header.save()
        return journal_header

    except Exception as e:
        logger.error(f"[EditJournalHeader] Error updating journal header: {str(e)}")
        raise TransactionMetadataUpdateError(f"Error updating journal header: {str(e)}")


def update_or_create_partner_entity_for_journal(
    cpf_cnpj: str, name: str, journal_header: JournalHeader
):
    cpf_cnpj = cpf_cnpj or None
    name = name or None

    partner_entity_id = journal_header.partner_entity_id

    if partner_entity_id:
        _update_existing_partner_entity(partner_entity_id, cpf_cnpj, name)
    else:
        _create_and_link_partner_entity(journal_header, cpf_cnpj, name)


def _update_existing_partner_entity(partner_entity_id, cpf_cnpj, name):
    partner = PartnerEntity.filter_one_by(id=partner_entity_id)

    if not cpf_cnpj and not name:
        partner.name = None
    elif cpf_cnpj and cpf_cnpj != partner.cpf_cnpj:
        partner.cpf_cnpj = cpf_cnpj
        if not partner.name:
            partner.name = _get_partner_name(cpf_cnpj)
    elif name and name != partner.name:
        partner.name = name

    partner.save()


def _get_partner_name(cpf_cnpj):
    partner_found = PartnerEntity.filter_one_by(cpf_cnpj=cpf_cnpj)
    if partner_found:
        return partner_found.name


def _create_and_link_partner_entity(journal_header, cpf_cnpj, name):
    if not cpf_cnpj and not name:
        return
    if not name:
        name = _get_partner_name(cpf_cnpj)

    partner = PartnerEntity(cpf_cnpj=cpf_cnpj, name=name)
    journal_header.partner_entity = partner
    partner.save()
