from copy import deepcopy
from datetime import datetime
from typing import Dict, List
from uuid import UUID

from application.account_configuration_service import set_account_configuration_batch
from application.camunda_service import CamundaService, extract_reconciliation_variables
from domain.models import (
    BankAccountStatus,
    JournalHeader,
    Reconciliation,
    ReconciliationStatusEnum,
)
from domain.repositories import (
    AbstractInvoiceRepository,
    AbstractPlatformTransactionRepository,
    AbstractReconciliationRepository,
)
from infrastructure.cockpit_api_client import CockpitApiClient
from infrastructure.invoices_api_client import InvoicesApiClient
from shared.auth import Session
from shared.logger import get_logger
from shared.utils.date import get_start_and_end_of_month

logger = get_logger()


class DraftReconciliationService:
    def __init__(
        self,
        repository: AbstractReconciliationRepository,
        platform_transaction_repository: AbstractPlatformTransactionRepository,
        invoice_repository: AbstractInvoiceRepository,
        session: Session,
        camunda_service: CamundaService = None,
        cockpit_api_client: CockpitApiClient = None,
    ):
        self.repository = repository
        self.platform_transaction_repository = platform_transaction_repository
        self.invoice_repository = invoice_repository
        self.session = session
        self.camunda_service = camunda_service or CamundaService()
        self.cockpit_api_client = cockpit_api_client or CockpitApiClient()

    async def find_existing_reconciliation(self, cockpit_customer_id, competence):
        """
        Retrieves an existing reconciliation for the given customer and competence.
        Returns None if not found.
        """
        reconciliations = (
            self.repository.get_reconciliations_by_customer_and_competence(
                cockpit_customer_id, competence
            )
        )
        return reconciliations[0] if reconciliations else None

    async def get_customer_cnpj(self, cockpit_customer_id: str) -> str:
        """
        Retrieves the CNPJ for a given customer from Cockpit API.
        Raises ValueError if not found.
        """
        customer_data = await self.cockpit_api_client.get_customer(cockpit_customer_id)
        customer_cnpj = customer_data.get("cnpj")
        if not customer_cnpj:
            raise ValueError("Could not retrieve customer CNPJ")
        return customer_cnpj

    async def handle_draft(self, cockpit_customer_id, competence, bank_account_guids):
        """
        Processes draft reconciliation requests by:
        1. Finding existing reconciliation or creating new one
        2. Synchronizing journal headers with current platform data
        3. Returning reconciliation with suggested account data loaded
        """
        reconciliation = await self.find_existing_reconciliation(
            cockpit_customer_id, competence
        )
        if not reconciliation:
            customer_cnpj = await self.get_customer_cnpj(cockpit_customer_id)
            reconciliation = await self.create_draft(
                cockpit_customer_id, customer_cnpj, competence, bank_account_guids
            )

        self.sync_draft_journals(reconciliation, bank_account_guids)

        return reconciliation

    async def create_draft(
        self,
        cockpit_customer_id: str,
        customer_cnpj: str,
        competence: str,
        bank_account_guids: List[str],
    ):
        """
        Creates a new draft reconciliation and triggers Camunda process start.
        """

        breakpoint()
        invoices = self.invoice_repository.get_by_cnpj(
            taker_document=customer_cnpj,
            issuer_document=customer_cnpj,
            competence=competence,
        )

        (
            journal_headers,
            latest_account_configuration_list,
        ) = self.platform_transaction_repository.get_transactions_by_bankaccounts(
            bank_account_guids,
            competence,
            invoices,
            customer_cnpj,
        )

        status_bank_account_guids = self.__process_bank_account_status(
            journal_headers, bank_account_guids
        )

        reconciliation = self.repository.create(
            cockpit_customer_id=cockpit_customer_id,
            customer_cnpj=customer_cnpj,
            operator=self.session.email,
            status=ReconciliationStatusEnum.DRAFT,
            competence=competence,
            bank_account_guids=bank_account_guids,
            status_bank_account_guids=status_bank_account_guids,
            journal_headers=journal_headers,
        )

        set_account_configuration_batch(
            latest_account_configuration_list, reconciliation.id
        )

        self.camunda_service.send_start_process(
            variables=extract_reconciliation_variables(reconciliation),
            reconciliation_id=str(reconciliation.id),
        )

        self.camunda_service.send_correlate_message(
            message_name="busca_disponibiliza_dados",
            variables=extract_reconciliation_variables(reconciliation),
            reconciliation_id=str(reconciliation.id),
        )

        logger.info(f"Queued process start for reconciliation: {reconciliation.id}")
        return reconciliation

    def __process_bank_account_status(
        self,
        journal_headers: List[JournalHeader],
        bank_account_guids: List[str],
        status_bank_account_guids: Dict[str, str] = {},
    ) -> Dict[str, str]:
        """
        Process bank account statuses based on journal headers.

        Args:
            journal_headers: List of journal header objects containing
            cockpit_account_id attribute
            bank_account_guids: List of bank account GUIDs to process

        Returns:
            Dictionary mapping bank account GUIDs to their statuses
        """
        if bank_account_guids is None or len(bank_account_guids) == 0:
            return {}

        status_dict = {
            str(guid): status_bank_account_guids.get(guid, None)
            or BankAccountStatus.NO_OPERATOR_FEEDBACK
            for guid in bank_account_guids
        }

        accounts_in_journals = set()
        for header in journal_headers:
            account_id = str(header.cockpit_account_id)
            if account_id:
                accounts_in_journals.add(account_id)

        for account_guid in status_dict:
            if (
                account_guid in accounts_in_journals
                and status_dict[account_guid] != BankAccountStatus.PENDING_SUGGESTIONS
            ):
                status_dict[account_guid] = BankAccountStatus.EXTRACT_IMPORTED

        return status_dict

    def _get_accounts_to_sync(
        self, reconciliation: Reconciliation, bank_account_guids: List[str]
    ) -> List[str]:
        """
        Determines which accounts need synchronization.
        Skips manual accounts that already have data.
        """
        uploaded_accounts = {
            str(f.cockpit_account_id) for f in reconciliation.file_uploads
        }

        accounts_to_sync = []
        for guid in bank_account_guids:
            account_str = str(guid)
            is_manual = account_str in uploaded_accounts

            if is_manual:
                has_data = any(
                    str(h.cockpit_account_id) == account_str
                    for h in reconciliation.journal_headers
                )

                if not has_data:
                    accounts_to_sync.append(guid)
            else:
                accounts_to_sync.append(guid)

        return accounts_to_sync

    def sync_draft_journals(
        self, reconciliation: Reconciliation, bank_account_guids: List[str]
    ):
        """
        Synchronizes the draft reconciliation with the current Journals tables.
        Logic:
        - Manual accounts (with upload): sync only if no data exists yet
        - Non-manual accounts (no upload): always sync
        """
        if reconciliation.status == ReconciliationStatusEnum.FINISHED:
            if not reconciliation.status_bank_account_guids:
                status_bank_account_guids = self.__process_bank_account_status(
                    reconciliation.journal_headers, bank_account_guids
                )
                reconciliation.status_bank_account_guids = status_bank_account_guids
                reconciliation.save(refresh=True)
            return reconciliation

        def set_removed(index):
            reconciliation.journal_headers[index].deleted = True
            reconciliation.journal_headers[index].deleted_at = datetime.now()

        def add_new(index):
            reconciliation.journal_headers.append(journal_headers[index])

        (
            journal_headers,
            _,
        ) = self.platform_transaction_repository.get_transactions_by_bankaccounts(
            bank_account_guids, reconciliation.competence, reconciliation.customer_cnpj
        )

        status_bank_account_guids = self.__process_bank_account_status(
            journal_headers,
            bank_account_guids,
            deepcopy(reconciliation.status_bank_account_guids) or {},
        )

        reconciliation.bank_account_guids = bank_account_guids
        reconciliation.status_bank_account_guids = status_bank_account_guids

        local_ids = list(
            map(lambda x: UUID(x.source_reference_id), reconciliation.journal_headers)
        )
        external_ids = list(map(lambda x: UUID(x.source_reference_id), journal_headers))

        set_local_ids = set(local_ids)
        set_external_ids = set(external_ids)

        removed_indexes = [
            i for i, uuid in enumerate(local_ids) if uuid not in set_external_ids
        ]

        for index in removed_indexes:
            set_removed(index)

        added_indexes = [
            i for i, uuid in enumerate(external_ids) if uuid not in set_local_ids
        ]

        for index in added_indexes:
            add_new(index)

        reconciliation.save(refresh=True)
        return reconciliation
