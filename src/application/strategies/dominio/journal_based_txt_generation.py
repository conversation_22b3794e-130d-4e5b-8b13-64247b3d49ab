import base64
from typing import List, Union

from application.strategies.dominio.txt_generation_strategy import (
    DATE_FORMAT,
    BaseTxtGenerationStrategy,
)
from domain.models import (
    InvoiceMotiveEnum,
    JournalHeader,
    JournalLine,
    LinkedInvoice,
    OperationTypeEnum,
    Reconciliation,
)


class JournalBasedTxtGeneration(BaseTxtGenerationStrategy):
    def generate_txt(self, reconciliation: Reconciliation) -> str:
        """Generate TXT file content from reconciliation data and return as
        base64 encoded string."""
        output_lines = [self._generate_header_line(reconciliation.customer_cnpj)]

        for journal_header in reconciliation.journal_headers:
            output_lines.append(self._generate_journal_header_line(journal_header))

            if len(journal_header.journal_lines) == 2:
                output_lines.append(
                    self._generate_simple_transaction_line(journal_header)
                )
            else:
                output_lines.extend(
                    self._generate_complex_transaction_lines(journal_header)
                )

        txt_content = "\n".join(output_lines).upper()
        return base64.b64encode(txt_content.encode("utf-8")).decode("utf-8")

    def _generate_header_line(self, cnpj: str) -> str:
        """Generate the header line with CNPJ information."""
        return f"|0000|{cnpj}|"

    def _generate_journal_header_line(self, journal_header: JournalHeader) -> str:
        """Generate the journal header line based on transaction type."""
        transaction_type = "X" if len(journal_header.journal_lines) == 2 else "V"
        return f"|6000|{transaction_type}||||"

    def _generate_simple_transaction_line(self, journal_header: JournalHeader) -> str:
        """Generate transaction line for simple transactions
        (exactly 2 journal lines)."""

        for journal_line in journal_header.journal_lines:
            if journal_line.line_type == OperationTypeEnum.DEBIT:
                debit_line = journal_line
            else:
                credit_line = journal_line

        transaction_type = "Pagamento" if journal_header.amount < 0 else "Recebimento"

        return self._build_transaction_line(
            date=journal_header.date.strftime(DATE_FORMAT),
            debit_account=self._sanitize_account_number(debit_line.account_id),
            credit_account=self._sanitize_account_number(credit_line.account_id),
            amount=abs(float(journal_header.amount)),
            description=self._generate_transaction_description(
                transaction_type=transaction_type,
                journal_header=journal_header,
                journal_item=debit_line,
            ),
        )

    def _generate_complex_transaction_lines(
        self,
        journal_header: JournalHeader,
    ) -> List[str]:
        """Generate transaction lines for complex transactions
        (multiple journal lines)."""
        lines = []
        for journal_line in journal_header.journal_lines:
            if journal_line.line_type == OperationTypeEnum.DEBIT:
                debit_account = journal_line.account_id
                credit_account = ""
            else:
                debit_account = ""
                credit_account = journal_line.account_id

            payment_text = (
                "Pagamento"
                if journal_line.line_type == OperationTypeEnum.DEBIT
                else "Recebimento"
            )
            payment_text = f"{payment_text} {journal_line.description}"

            lines.append(
                self._build_transaction_line(
                    date=journal_header.date.strftime(DATE_FORMAT),
                    debit_account=self._sanitize_account_number(debit_account),
                    credit_account=self._sanitize_account_number(credit_account),
                    amount=abs(float(journal_line.amount)),
                    description=self._generate_transaction_description(
                        payment_text, journal_header, journal_line
                    ),
                )
            )

        return lines

    def _build_transaction_line(
        self,
        date: str,
        debit_account: str,
        credit_account: str,
        amount: float,
        description: str,
    ) -> str:
        """Build a standardized transaction line with the given parameters."""
        amount_str = f"{amount:.2f}".replace(".", ",")
        return f"|6100|{date}|{debit_account}|{credit_account}|{amount_str}||{description}||||"  # noqa

    def _generate_transaction_description(
        self,
        transaction_type: str,
        journal_header: JournalHeader,
        journal_item: Union[JournalLine, JournalHeader],
    ) -> str:
        """Generate a human-readable description for the transaction."""

        transaction_description = journal_header.description or ""
        notes = journal_header.notes or ""
        trading_name = (
            journal_header.partner_entity.name
            if journal_header.partner_entity is not None
            else ""
        )
        invoice_text = self._get_invoice_text(journal_item)

        description_fields = [
            transaction_type,
            transaction_description,
            trading_name,
            invoice_text,
        ]

        metadata = journal_item._metadata
        motive = metadata.get("invoice_motive") if metadata else None

        if metadata:
            if (
                metadata["is_linked_invoice_applicable"] is False
                and (
                    metadata.get("linked_invoices")
                    and len(metadata.get("linked_invoices")) != 0
                )
            ) or (motive and motive == InvoiceMotiveEnum.MISSING_INVOICE):
                description_fields = [
                    transaction_type,
                    invoice_text,
                    trading_name,
                    transaction_description,
                ]
            elif metadata.get("has_linked_invoices") is False:
                description_fields = [
                    transaction_type,
                    transaction_description,
                    trading_name,
                    notes,
                ]
            elif motive != InvoiceMotiveEnum.MISSING_INVOICE:
                description_fields = [
                    transaction_type,
                    motive,
                    trading_name,
                    transaction_description,
                    notes,
                ]

        return " - ".join(
            filter(
                bool,
                description_fields,
            )
        ).strip()

    def _sanitize_account_number(self, account_number: str) -> str:
        """Remove non-digit characters from account number."""
        return "".join(filter(str.isdigit, account_number))

    def _get_invoice_status(self, journal_line: JournalLine):
        metadata = journal_line._metadata
        if metadata is None:
            return None

        if metadata.get("has_linked_invoices", False) is False:
            return "Não possui NF"
        if metadata.get("is_linked_invoice_applicable", False) is False:
            return "Não se aplica"
        return None

    def _get_invoice_text(self, journal_item: Union[JournalLine, JournalHeader]):
        invoice_status = self._get_invoice_status(journal_item)

        invoice_numbers = []
        linked_invoices: List[LinkedInvoice] = journal_item.linked_invoices

        if linked_invoices:
            for linked_invoice in linked_invoices:
                if linked_invoice.invoice_number:
                    invoice_numbers.append(linked_invoice.invoice_number)

        if invoice_status:
            return invoice_status

        if invoice_numbers:
            return f"NF {','.join(invoice_numbers)}"

        return ""
