from abc import ABC, abstractmethod

import pandas as pd
from openpyxl.styles import Font, PatternFill

from infrastructure.cockpit_api_client import CockpitApiClient
from shared.auth import Session

DATE_FORMAT = "%d/%m/%Y"


class ExcelGenerationStrategy(ABC):
    @abstractmethod
    def generate_excel(self, reconciliation, session) -> str:
        pass

    def __get_bankaccount_name(self, cockpit_account_id: str, session: Session):
        name = str(cockpit_account_id)[:31]

        try:
            cockpit_api_client = CockpitApiClient(session)
            response = cockpit_api_client.get_bankaccount_by_guid(
                bankaccount_guid=cockpit_account_id
            )

            results = response.json().get("results", [])

            if len(results) == 1:
                bank = results[0]
                return (
                    f"{bank['description']}-{bank['agency_code']}-{bank['account_id']}"
                )
        except Exception:
            pass

        return name

    def __style_reconciliation_sheet(self, writer, sheet_name):
        workbook = writer.book
        sheet = workbook[sheet_name]

        header_fill = PatternFill(
            start_color="FFFF99", end_color="FFFF99", fill_type="solid"
        )
        header_font = Font(color="000000", bold=True)

        for cell in sheet[1]:
            cell.fill = header_fill
            cell.font = header_font

        for col in sheet.columns:
            max_length = 0
            column = col[0].column_letter

            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(cell.value)
                except Exception:
                    pass

            adjusted_width = max_length + 2
            sheet.column_dimensions[column].width = adjusted_width

    def __generate_common_excel(self, all_data, writer):
        df = pd.DataFrame(all_data)
        df_filtered = df[["Data", "Crédito", "Débito", "Valor", "Histórico"]]
        df_filtered["Crédito"] = (
            df_filtered["Crédito"].astype(str).str.split("-").str[0]
        )
        df_filtered["Débito"] = df_filtered["Débito"].astype(str).str.split("-").str[0]
        df_filtered["Lote"] = ""
        df_filtered["Data"] = pd.to_datetime(
            df_filtered["Data"],
            format=DATE_FORMAT,
            errors="coerce",
        )
        df_filtered = df_filtered.sort_values(
            by=["Data", "Valor"],
            ascending=[True, False],
            na_position="first",
        )
        df_filtered["Data"] = df_filtered["Data"].dt.strftime(DATE_FORMAT)
        df_filtered["Data"] = df_filtered["Data"].fillna("")
        sheet_name = "Consolidado"
        df_filtered.to_excel(writer, sheet_name=sheet_name, index=False)
        self.__style_reconciliation_sheet(writer=writer, sheet_name=sheet_name)
