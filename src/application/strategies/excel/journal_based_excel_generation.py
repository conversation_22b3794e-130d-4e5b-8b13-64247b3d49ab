import base64
import io
from typing import Any, Dict, List, Union

import pandas as pd
from sqlalchemy.orm import Session

from application.account_configuration_service import get_default_accounts
from application.strategies.excel.excel_generation_strategy import (
    DATE_FORMAT,
    ExcelGenerationStrategy,
)
from domain.models import JournalHeader, JournalLine, OperationTypeEnum, Reconciliation
from infrastructure.repositories.account_configuration_repository import (
    AccountConfigurationRepository,
)


class JournalBasedExcelGeneration(ExcelGenerationStrategy):
    def generate_excel(self, reconciliation: Reconciliation, session: Session) -> str:
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            grouped_journals: Dict[str, List[Dict[str, Any]]] = {}
            all_data: List[Dict[str, Any]] = []

            journal_headers: List[JournalHeader] = reconciliation.journal_headers

            for journal_header in journal_headers:
                self._process_journal_header(
                    journal_header,
                    reconciliation.id,
                    grouped_journals,
                    all_data,
                )

            if not grouped_journals:
                default_sheet_name = "Lançamentos"
                grouped_journals[default_sheet_name] = []
                all_data = [
                    {
                        "Data": None,
                        "Crédito": "",
                        "Débito": "",
                        "Valor": "",
                        "Histórico": "",
                    }
                ]

            self._generate_account_sheets(writer, grouped_journals, session)
            self._ExcelGenerationStrategy__generate_common_excel(all_data, writer)

        output.seek(0)
        return base64.b64encode(output.read()).decode("utf-8")

    def _process_journal_header(
        self,
        journal_header: JournalHeader,
        reconciliation_id: str,
        grouped_journals: Dict[str, List[Dict[str, Any]]],
        all_data: List[Dict[str, Any]],
    ) -> None:
        cockpit_account_id: str = journal_header.cockpit_account_id or "Conta"
        journal_type_text: str = "Entrada" if journal_header.amount > 0 else "Saída"
        trading_name_text: str = (
            journal_header.partner_entity.name if journal_header.partner_entity else ""
        )

        if len(journal_header.journal_lines) == 2:
            self._process_simple_journal(
                journal_header,
                journal_type_text,
                trading_name_text,
                cockpit_account_id,
                grouped_journals,
                all_data,
            )
        else:
            self._process_complex_journal(
                journal_header,
                trading_name_text,
                cockpit_account_id,
                grouped_journals,
                all_data,
                reconciliation_id,
            )

    def _process_simple_journal(
        self,
        journal_header: JournalHeader,
        journal_type_text: str,
        trading_name_text: str,
        cockpit_account_id: str,
        grouped_journals: Dict[str, List[Dict[str, Any]]],
        all_data: List[Dict[str, Any]],
    ) -> None:
        base_row: Dict[str, Any] = self._create_base_row(
            journal_header, journal_type_text, trading_name_text
        )
        invoices_number: List[str] = []

        for journal_line in journal_header.journal_lines:
            if journal_line.line_type == OperationTypeEnum.DEBIT:
                base_row["Débito"] = journal_line.account_id
            elif journal_line.line_type == OperationTypeEnum.CREDIT:
                base_row["Crédito"] = journal_line.account_id

            invoices_number.extend(self._get_invoice_numbers(journal_line))

        base_row["Num NF"] = ",".join(invoices_number)
        base_row["Histórico"] = self._get_history(
            "Pagamento" if journal_header.amount < 0 else "Recebimento",
            journal_header,
            invoices_number,
            trading_name_text,
        )

        self._add_to_grouped_journals(grouped_journals, cockpit_account_id, base_row)
        all_data.append(base_row)

    def _process_complex_journal(
        self,
        journal_header: JournalHeader,
        trading_name_text: str,
        cockpit_account_id: str,
        grouped_journals: Dict[str, List[Dict[str, Any]]],
        all_data: List[Dict[str, Any]],
        reconciliation_id: str,
    ) -> None:
        account_configurations = (
            AccountConfigurationRepository().get_by_reconciliation_and_account(
                reconciliation_id=reconciliation_id,
                cockpit_account_id=cockpit_account_id,
            )
        )

        for journal_line in journal_header.journal_lines:
            base_row: Dict[str, Any] = self._create_base_row(
                journal_header,
                (
                    "Entrada"
                    if journal_line.line_type == OperationTypeEnum.CREDIT
                    else "Saída"
                ),
                trading_name_text,
            )
            default_debit: str
            default_credit: str
            default_debit, default_credit = get_default_accounts(
                journal_header.journal_lines, account_configurations
            )

            if journal_line.line_type == OperationTypeEnum.DEBIT:
                base_row["Débito"] = journal_line.account_id
                base_row["Crédito"] = default_debit
            elif journal_line.line_type == OperationTypeEnum.CREDIT:
                base_row["Crédito"] = journal_line.account_id
                base_row["Débito"] = default_credit

            invoices_number: List[str] = self._get_invoice_numbers(journal_line)

            base_row["Num NF"] = ",".join(invoices_number)
            base_row["Histórico"] = self._get_history(
                (
                    "Pagamento"
                    if journal_line.line_type == OperationTypeEnum.DEBIT
                    else "Recebimento"
                ),
                journal_line,
                invoices_number,
                trading_name_text,
            )
            base_row["Valor"] = abs(float(journal_line.amount))

            self._add_to_grouped_journals(
                grouped_journals, cockpit_account_id, base_row
            )
            all_data.append(base_row)

    def __to_upper(self, text: str) -> str:
        if text:
            return text.upper()
        return text

    def _create_base_row(
        self,
        journal_header: JournalHeader,
        journal_type_text: str,
        trading_name_text: str,
    ) -> Dict[str, Any]:
        return {
            "Data": journal_header.date.strftime(DATE_FORMAT),
            "Tipo": self.__to_upper(journal_type_text),
            "Descrição": self.__to_upper(journal_header.description),
            "CNPJ/CPF": (
                journal_header.partner_entity.cpf_cnpj
                if journal_header.partner_entity
                else None
            ),
            "Beneficiário": self.__to_upper(trading_name_text),
            "Débito": None,
            "Crédito": None,
            "Valor": abs(float(journal_header.amount)),
            "Num NF": None,
            "Observação": self.__to_upper(journal_header.notes),
            "Histórico": None,
        }

    def _get_invoice_numbers(self, journal_line: JournalLine) -> List[str]:
        return [
            inv.invoice_number
            for inv in journal_line.linked_invoices
            if inv.invoice_number
        ]

    def _add_to_grouped_journals(
        self,
        grouped_journals: Dict[str, List[Dict[str, Any]]],
        cockpit_account_id: str,
        row: Dict[str, Any],
    ) -> None:
        if cockpit_account_id not in grouped_journals:
            grouped_journals[cockpit_account_id] = []
        grouped_journals[cockpit_account_id].append(row)

    def _get_history(
        self,
        payment_text: str,
        journal_item: Union[JournalHeader, JournalLine],
        invoice_numbers: List[str],
        trading_name_text: str,
    ) -> str:
        invoice_status = self._get_invoice_status(journal_item)
        invoice_text = (
            f"NF {','.join(invoice_numbers)}" if invoice_numbers else invoice_status
        )
        parts = [payment_text, invoice_text, trading_name_text]
        return " - ".join(filter(bool, parts)).upper()

    def _get_invoice_status(
        self, journal_item: Union[JournalHeader, JournalLine]
    ) -> Union[str, None]:
        metadata = getattr(journal_item, "_metadata", {})
        if metadata is None:
            return None

        if metadata.get("has_linked_invoices") is False:
            return "Não possui NF"
        if metadata.get("is_linked_invoice_applicable") is False:
            return "Não se aplica"
        return None

    def _generate_account_sheets(
        self,
        writer: pd.ExcelWriter,
        grouped_journals: Dict[str, List[Dict[str, Any]]],
        session: Session,
    ) -> None:
        for cockpit_account_id, data in grouped_journals.items():
            df = pd.DataFrame(data)
            sheet_name = self._ExcelGenerationStrategy__get_bankaccount_name(
                cockpit_account_id=cockpit_account_id, session=session
            )
            df.to_excel(writer, sheet_name=sheet_name, index=False)
            self._ExcelGenerationStrategy__style_reconciliation_sheet(
                writer=writer, sheet_name=sheet_name
            )
