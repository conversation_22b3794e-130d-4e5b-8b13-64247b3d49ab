from typing import List

from application.transaction_service import update_journals_account_data
from domain.exceptions import (
    MultipleChartOfAccountsAccountConfigurationError,
    ReconciliationNotFoundException,
    ReconciliationStatusException,
)
from domain.models import (
    AccountConfiguration,
    AccountConfigurationType<PERSON>num,
    JournalLine,
    OperationTypeEnum,
    ReconciliationStatusEnum,
)
from infrastructure.repositories.account_configuration_repository import (
    AccountConfigurationRepository,
)
from infrastructure.repositories.reconciliation_repository import (
    ReconciliationRepository,
)


def set_account_configuration_batch(
    account_configuration_list: list, reconciliation_id: str
):
    account_configuration_repository = AccountConfigurationRepository()

    return account_configuration_repository.set_account_configuration_batch(
        account_configuration_list=account_configuration_list,
        account_configuration_type=AccountConfigurationTypeEnum.CHART_OF_ACCOUNTS_CODE,
        reconciliation_id=reconciliation_id,
    )


def set_account_configuration(
    cockpit_account_id: str,
    bank_code: str,
    account_configuration_type: AccountConfigurationTypeEnum,
    reconciliation_id: str,
):
    account_configuration_repository = AccountConfigurationRepository()
    reconciliation_repository = ReconciliationRepository()

    reconciliation = reconciliation_repository.get_reconciliation(reconciliation_id)

    if reconciliation is None:
        raise ReconciliationNotFoundException

    if (
        ReconciliationStatusEnum(reconciliation.status)
        == ReconciliationStatusEnum.FINISHED
    ):
        raise ReconciliationStatusException

    existing_configuration_list = (
        account_configuration_repository.get_by_reconciliation_and_account(
            reconciliation_id=reconciliation_id, cockpit_account_id=cockpit_account_id
        )
    )

    if len(existing_configuration_list) == 1:
        existing_configuration = existing_configuration_list[0]
        existing_configuration.value = bank_code
        existing_configuration.type = account_configuration_type

        if (
            account_configuration_type
            == AccountConfigurationTypeEnum.CHART_OF_ACCOUNTS_CODE
        ):
            update_journals_account_data(bank_code, reconciliation_id)

        return account_configuration_repository.update(existing_configuration)
    else:
        account_configuration = account_configuration_repository.create(
            cockpit_account_id=cockpit_account_id,
            bank_code=bank_code,
            account_configuration_type=account_configuration_type,
            reconciliation_id=reconciliation_id,
        )

        if (
            account_configuration_type
            == AccountConfigurationTypeEnum.CHART_OF_ACCOUNTS_CODE
        ):
            update_journals_account_data(bank_code, reconciliation_id)

        return account_configuration


def get_by_reconciliation_and_account(
    cockpit_account_id: str,
    reconciliation_id: str,
):
    account_configuration_repository = AccountConfigurationRepository()

    existing_configuration_list = (
        account_configuration_repository.get_by_reconciliation_and_account(
            reconciliation_id=reconciliation_id, cockpit_account_id=cockpit_account_id
        )
    )

    return existing_configuration_list


def get_default_accounts(
    journal_lines: List[JournalLine],
    account_configurations: List[AccountConfiguration],
) -> tuple[str, str]:
    """Get default debit and credit accounts from configurations
    or journal lines."""
    chart_of_accounts_config = [
        config
        for config in account_configurations
        if config.type == AccountConfigurationTypeEnum.CHART_OF_ACCOUNTS_CODE
    ]

    if len(chart_of_accounts_config) > 1:
        raise MultipleChartOfAccountsAccountConfigurationError()

    if chart_of_accounts_config:
        default_account = chart_of_accounts_config[0].value
        return default_account, default_account

    default_debit = next(
        (
            line.account_id
            for line in journal_lines
            if line.line_type == OperationTypeEnum.DEBIT
        ),
        None,
    )
    default_credit = next(
        (
            line.account_id
            for line in journal_lines
            if line.line_type == OperationTypeEnum.CREDIT
        ),
        None,
    )

    return default_debit, default_credit
