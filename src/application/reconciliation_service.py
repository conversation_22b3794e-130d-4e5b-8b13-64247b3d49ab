from copy import deepcopy
from typing import Dict

from application.camunda_service import CamundaService, extract_reconciliation_variables
from application.strategies.dominio.journal_based_txt_generation import (
    JournalBasedTxtGeneration,
)
from application.strategies.excel.journal_based_excel_generation import (
    JournalBasedExcelGeneration,
)
from domain.exceptions import (
    BankAccountStatusNotFoundException,
    ReconciliationNotFinishedException,
    ReconciliationNotFoundException,
    ReconciliationStatusException,
)
from domain.models import (
    BankAccountStatus,
    ReconciliationStatusEnum,
    TransactionStatusEnum,
)
from infrastructure.repositories.reconciliation_repository import (
    ReconciliationRepository,
)

DATE_FORMAT = "%d/%m/%Y"

camunda_service = CamundaService()


def get_reconciliation(reconciliation_id: str):
    reconciliation_repository = ReconciliationRepository()

    reconciliation = reconciliation_repository.get_reconciliation(reconciliation_id)

    if not reconciliation:
        raise ReconciliationNotFoundException

    return reconciliation


def set_reconciliation_status(  # noqa
    reconciliation_id: str, status: ReconciliationStatusEnum
):
    reconciliation_repository = ReconciliationRepository()

    reconciliation = reconciliation_repository.get_reconciliation(reconciliation_id)

    if not reconciliation:
        raise ReconciliationNotFoundException

    if (
        ReconciliationStatusEnum(reconciliation.status)
        == ReconciliationStatusEnum.DRAFT
    ):
        if (
            status == ReconciliationStatusEnum.ACTIVE
            or status == ReconciliationStatusEnum.CANCELLED
        ):
            camunda_service.send_correlate_message(
                message_name="inicia_conciliacao",
                variables=extract_reconciliation_variables(reconciliation),
                reconciliation_id=str(reconciliation.id),
            )
            return reconciliation_repository.set_status(reconciliation, status)
        else:
            raise ReconciliationStatusException

    if (
        ReconciliationStatusEnum(reconciliation.status)
        == ReconciliationStatusEnum.ACTIVE
    ):
        if status == ReconciliationStatusEnum.FINISHED:
            if reconciliation.status_bank_account_guids:
                for account_status in reconciliation.status_bank_account_guids.values():
                    if BankAccountStatus(account_status) not in {
                        BankAccountStatus.EXTRACT_IMPORTED,
                        BankAccountStatus.FINISH_WITHOUT_EXTRACT,
                        BankAccountStatus.FINISH_WITHOUT_TRANSACTIONS,
                    }:
                        raise ReconciliationStatusException

            for journal_header in reconciliation.journal_headers:
                if (
                    TransactionStatusEnum(journal_header.status)
                    != TransactionStatusEnum.RECONCILED
                ):
                    raise ReconciliationStatusException

            camunda_service.send_correlate_message(
                message_name="finaliza_conciliacao",
                variables=extract_reconciliation_variables(reconciliation),
                reconciliation_id=str(reconciliation.id),
            )
        return reconciliation_repository.set_status(reconciliation, status)
    else:
        raise ReconciliationStatusException


def set_reconciliation_bank_status(
    reconciliation_id: str, bank_account_status: Dict[str, BankAccountStatus]
):
    reconciliation_repository = ReconciliationRepository()

    reconciliation = reconciliation_repository.get_reconciliation(reconciliation_id)

    if not reconciliation:
        raise ReconciliationNotFoundException

    if reconciliation.status != ReconciliationStatusEnum.ACTIVE:
        raise BankAccountStatusNotFoundException

    status_bank_account_guids = deepcopy(reconciliation.status_bank_account_guids)

    if not status_bank_account_guids:
        status_bank_account_guids = {}

    updated = False

    for account_id, status in bank_account_status.items():
        status_bank_account_guids[account_id] = status
        updated = True

    if not updated:
        raise BankAccountStatusNotFoundException

    reconciliation_repository.set_bank_status(reconciliation, status_bank_account_guids)

    return bank_account_status


def download_excel_file(reconciliation_id: str, session):
    reconciliation_repository = ReconciliationRepository()

    reconciliation = reconciliation_repository.get_reconciliation(reconciliation_id)

    if not reconciliation:
        raise ReconciliationNotFoundException

    if (
        ReconciliationStatusEnum(reconciliation.status)
        != ReconciliationStatusEnum.FINISHED
    ):
        raise ReconciliationNotFinishedException

    strategy = JournalBasedExcelGeneration()

    return strategy.generate_excel(reconciliation, session)


def download_txt_file(reconciliation_id: str):
    reconciliation_repository = ReconciliationRepository()

    reconciliation = reconciliation_repository.get_reconciliation(reconciliation_id)

    if not reconciliation:
        raise ReconciliationNotFoundException

    if (
        ReconciliationStatusEnum(reconciliation.status)
        != ReconciliationStatusEnum.FINISHED
    ):
        raise ReconciliationNotFinishedException

    strategy = JournalBasedTxtGeneration()

    return strategy.generate_txt(reconciliation)
