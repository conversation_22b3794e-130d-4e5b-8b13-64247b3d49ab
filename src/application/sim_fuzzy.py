import re

import unidecode
from rapidfuzz import fuzz


def clean_name(name: str) -> str:
    if not name:
        return ""
    name = re.sub(r"^[\d\.\-/ ]+", "", name)
    name = unidecode.unidecode(name)
    name = re.sub(r"[^a-zA-Z\s]", "", name)
    tokens = [t for t in name.strip().lower().split()]
    return " ".join(tokens)


def first_relevant_token(name: str) -> str:
    stopwords = {
        "empresa",
        "ltda",
        "me",
        "eireli",
        "sa",
        "s/a",
        "the",
        "inc",
        "group",
        "holdings",
        "comercial",
        "comercio",
        "servicos",
        "consultoria",
        "produtos",
        "sistemas",
        "tecnologia",
        "industria",
        "associados",
        "associacao",
        "sociedade",
        "fundacao",
        "instituto",
        "cooperativa",
        "digital",
        "online",
        "brasil",
        "ltd",
        "ltda",
        "eireli",
        "sa",
        "s/a",
        "the",
        "inc",
        "group",
        "holdings",
        "comercial",
        "comercio",
        "servicos",
        "consultoria",
        "produtos",
        "sistemas",
        "tecnologia",
        "industria",
        "associados",
        "associacao",
        "sociedade",
        "fundacao",
        "instituto",
        "cooperativa",
        "digital",
        "online",
        "brasil",
        "ltd",
        "ltda",
        "eireli",
        "sa",
        "s/a",
        "the",
        "inc",
        "group",
        "holdings",
        "comercial",
        "comercio",
        "servicos",
        "consultoria",
        "produtos",
        "sistemas",
        "tecnologia",
        "industria",
        "associados",
        "associacao",
        "sociedade",
        "fundacao",
        "instituto",
        "cooperativa",
        "digital",
        "online",
        "brasil",
        "ltd",
        "ltda",
        "eireli",
        "sa",
        "s/a",
        "the",
        "inc",
        "group",
        "holdings",
    }
    tokens = [t for t in clean_name(name).split() if t not in stopwords]
    return tokens[0] if tokens else ""


def name_similarity_fuzzy(name1: str, name2: str) -> float:
    n1 = clean_name(name1)
    n2 = clean_name(name2)
    if not n1 or not n2:
        return 0.0
    if (
        first_relevant_token(name1)
        and first_relevant_token(name2)
        and first_relevant_token(name1) != first_relevant_token(name2)
    ):
        return 0.0
    if n1 == n2:
        return 1.0
    tsr = fuzz.token_set_ratio(n1, n2)
    pr = fuzz.partial_ratio(n1, n2)
    score = max(tsr, pr) / 100.0
    tokens1 = set(n1.split())
    tokens2 = set(n2.split())
    inter = tokens1 & tokens2
    if len(inter) == 0:
        return 0.0
    if score < 0.7:
        return score * 0.5
    return score
