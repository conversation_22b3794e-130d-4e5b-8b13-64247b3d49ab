import uuid
from typing import Generator, List, Optional

from config.config import settings
from domain.models import Invoice, JournalHeader, SuggestedInvoice, SuggestionTypeEnum
from infrastructure.adapters.invoice_categorizer import InvoiceCategorizer
from infrastructure.adapters.simplified_categorizer import SimplifiedCategorizer
from infrastructure.repositories.account_repository import AccountRepository
from shared.logger import get_logger
from shared.session_manager import session_scope

logger = get_logger()


class CategoryService:
    def __init__(self):
        self.categorizer: Optional[SimplifiedCategorizer] = None
        self.invoice_categorizer: Optional[InvoiceCategorizer] = None
        self.suggestions_buffer: List[SuggestedInvoice] = []

    def initialize_for_customer(self, customer_cnpj: str) -> bool:
        """Initialize categorizer with customer's chart of accounts"""
        if not settings.FEATURE_FLAG_SIMPLIFIED_CATEGORIZER:
            return False

        account_repo = AccountRepository()
        accounts = account_repo.get_analytic_accounts_by_cnpj(customer_cnpj)

        if not accounts:
            return False

        self.categorizer = SimplifiedCategorizer(accounts)
        self.invoice_categorizer = InvoiceCategorizer(accounts)
        self.suggestions_buffer = []
        return True

    def categorize_invoice(
        self, journal_header: JournalHeader, invoices: List[Invoice]
    ) -> bool:
        """Dynamic categorize a single journal"""
        if not self.invoice_categorizer:
            return False

        result = self.invoice_categorizer.suggest_invoices_for_transaction(
            journal_header,
            invoices,
        )

        categorization_result = {
            "version": "invoice_categorizer_v1",
            "confidence": result[0][1],
            "suggestion_type": SuggestionTypeEnum.INVOICE,
        }

        if result:
            invoice_data = result[0][0] if result[0] else None
            suggestion = self._create_suggestion(
                journal_header,
                categorization_result,
                invoice_data=invoice_data,
            )
            self.suggestions_buffer.append(suggestion)
            return True

        return False

    def categorize_journal(self, journal_header: JournalHeader) -> bool:
        """Categorize a single journal and buffer suggestion"""
        if not self.categorizer:
            return False

        try:
            result = self.categorizer.categorize_journal(journal_header)
            if result:
                result.update(
                    {
                        "version": "simplified_categorizer_v1",
                        "suggestion_type": SuggestionTypeEnum.ACCOUNT,
                    }
                )
                suggestion = self._create_suggestion(journal_header, result)
                self.suggestions_buffer.append(suggestion)
                return True
        except Exception as e:
            logger.warning(
                f"Categorization failed for journal "
                f"{journal_header.source_reference_id}: {e}"
            )
        return False

    def finalize(self):
        """Finalize categorization process - save suggestions and log statistics"""
        if self.suggestions_buffer:
            self._save_suggestions_batch()

        if self.categorizer or self.invoice_categorizer:
            self._log_categorization_stats()

    def _create_suggestion(
        self,
        journal_header,
        categorization_result,
        invoice_data=None,
    ):
        """Create a SuggestedInvoice object for account or invoice suggestion"""
        suggested_invoice = SuggestedInvoice(
            suggestion_id=str(uuid.uuid4()),
            platform_transaction_id=journal_header.source_reference_id,
            cockpit_account_id=journal_header.cockpit_account_id,
            suggestion_type=categorization_result["suggestion_type"],
            ranking=categorization_result["confidence"],
            version=categorization_result["version"],
        )

        if (
            categorization_result["suggestion_type"] == SuggestionTypeEnum.ACCOUNT
            and categorization_result["version"] == "simplified_categorizer_v1"
        ):
            suggested_invoice.account_data = {
                "suggested_account_id": categorization_result["account_id"],
                "suggested_account_code": categorization_result["account_code"],
                "suggested_account_description": categorization_result[
                    "account_description"
                ],
                "number": categorization_result["account_code"],
                "rule_name": categorization_result["rule_name"],
                "confidence": categorization_result["confidence"],
                "classification": categorization_result["classification"],
            }

        if (
            categorization_result["suggestion_type"] == SuggestionTypeEnum.INVOICE
            and invoice_data
        ):
            suggested_invoice.platform_invoice_id = invoice_data.get(
                "platform_invoice_id"
            ) or invoice_data.get("id")
            suggested_invoice.invoice_date = invoice_data.get("issueDate")
            suggested_invoice.invoice_company_name = invoice_data.get(
                "takerName"
            ) or invoice_data.get("issuerName")
            suggested_invoice.invoice_value = invoice_data.get("amount")
            suggested_invoice.invoice_number = invoice_data.get("number")

        return suggested_invoice

    def _save_suggestions_batch(self):
        """Save all suggestions in a single database transaction"""
        if not self.suggestions_buffer:
            return
        try:
            with session_scope(close=False) as db_session:
                db_session.add_all(self.suggestions_buffer)
                db_session.commit()
                logger.info(f"Saved {len(self.suggestions_buffer)} account suggestions")

            self.suggestions_buffer.clear()

        except Exception as e:
            logger.error(f"Error saving suggestions batch: {e}")
            raise

    def _log_categorization_stats(self):
        """Log categorization statistics"""
        stats = self.categorizer.get_stats() or self.invoice_categorizer.get_stats()
        coverage = (
            (stats["total_categorized"] / stats["total_processed"] * 100)
            if stats["total_processed"] > 0
            else 0
        )

        top_rules = dict(
            sorted(stats["by_rule"].items(), key=lambda x: x[1], reverse=True)[:5]
        )

        logger.info(
            f"Categorization completed - "
            f"Coverage: {coverage:.1f}% "
            f"({stats['total_categorized']}/{stats['total_processed']}), "
            f"Time: {stats['processing_time_ms']:.2f}ms, "
            f"Top rules: {top_rules}"
        )
