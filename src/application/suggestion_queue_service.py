from functools import lru_cache
from typing import List

from domain.models import SuggestedInvoice, SuggestionSource, SuggestionTypeEnum
from domain.repositories import AbstractMatchRepository
from infrastructure.invoices_api_client import InvoicesApiClient
from shared.logger import get_logger
from shared.utils.conversor import convert_to_suggested_invoice

logger = get_logger()


class SuggestionQueue:
    def __init__(
        self,
        repository: AbstractMatchRepository,
        invoices_api_client: InvoicesApiClient,
    ):
        self.repository = repository
        self.invoices_api_client = invoices_api_client

    def import_match(self, suggestion) -> List[SuggestedInvoice]:  # noqa
        def update_data(new, suggested_invoice):
            suggested_invoice.suggestion_id = new.suggestion_id
            suggested_invoice.cockpit_account_id = new.cockpit_account_id
            suggested_invoice.ranking = new.ranking
            suggested_invoice.exact_match = new.exact_match
            suggested_invoice.version = new.version
            suggested_invoice.should_not_be_matched = new.should_not_be_matched
            if new.account_data:
                suggested_invoice.account_data = new.account_data

            return suggested_invoice

        def add_details(suggested_invoice, invoice):
            if (
                not invoice
                or suggested_invoice.suggestion_type != SuggestionTypeEnum.INVOICE
            ):
                return suggested_invoice

            suggested_invoice.invoice_date = invoice.get("issueDate")
            suggested_invoice.invoice_company_name = (
                invoice.get("takerName")
                if invoice.get("issuerDocument") == suggestion["cnpj_cliente_bhub"]
                else invoice.get("issuerName")
            )
            suggested_invoice.invoice_value = invoice.get("amount")
            suggested_invoice.invoice_number = invoice.get("number")
            return suggested_invoice

        suggested_invoices: List[SuggestedInvoice] = convert_to_suggested_invoice(
            suggestion, SuggestionSource.BINDER
        )

        invoice_ids = [
            si.platform_invoice_id
            for si in suggested_invoices
            if si.suggestion_type == SuggestionTypeEnum.INVOICE
            and si.platform_invoice_id
        ]
        invoices = []
        if invoice_ids:
            invoices = self.invoices_api_client.get_invoices_by_ids(invoice_ids)

        search_invoice_ids = list(map(lambda x: x.get("id"), invoices))

        @lru_cache(maxsize=None)
        def get_invoice(platform_invoice_id):
            try:
                index = search_invoice_ids.index(platform_invoice_id)
                return invoices[index]
            except ValueError:
                return []

        updated = []

        for suggested_invoice in suggested_invoices:
            existing = self.repository.get_by_platform_id(
                suggested_invoice.platform_transaction_id,
                suggested_invoice.platform_invoice_id,
                suggested_invoice.suggestion_type,
            )

            if suggested_invoice.suggestion_type == SuggestionTypeEnum.INVOICE:
                invoice = get_invoice(suggested_invoice.platform_invoice_id)
                if existing:
                    updated.append(
                        add_details(update_data(suggested_invoice, existing), invoice)
                    )
                else:
                    updated.append(add_details(suggested_invoice, invoice))
            else:
                if existing:
                    updated.append(update_data(suggested_invoice, existing))
                else:
                    updated.append(suggested_invoice)

        self.repository.create_or_update(updated)
        get_invoice.cache_clear()

        return updated
