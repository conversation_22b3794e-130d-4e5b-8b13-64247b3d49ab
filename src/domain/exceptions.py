class ReconciliationStatusException(Exception):
    """Exception raised when it's not acceptable
    to change reconciliation status"""

    def __init__(self):
        msg = """
        Error: It is not possible to change the reconciliation status.

        This status is in a state that does not allow changes.
        Please check if all necessary conditions have been met
        before attempting to modify the status again.
        """
        super().__init__(msg)


class ReconciliationNotFoundException(Exception):
    """Exception raised when reconciliation was not found"""

    def __init__(self, message: str = ""):
        msg = f"Error: Reconciliation ID not found.{message}"
        super().__init__(msg)


class ReconciliationNotFinishedException(Exception):
    """Exception raised when you try to download excel
    for a not finished reconciliation"""

    def __init__(self, message: str = ""):
        msg = f"Error: Reconciliation status is not finished.{message}"
        super().__init__(msg)


class TransactionNotFoundException(Exception):
    """Exception raised when transaction was not found"""

    def __init__(self, message: str = ""):
        msg = f"Error: Transaction ID not found.{message}"
        super().__init__(msg)


class TransactionStatusCannotBeChangedException(Exception):
    """Exception raised when transaction is not in NEW status"""

    def __init__(self, message: str = ""):
        msg = f"Error: Transaction status can only be changed from NEW.{message}"
        super().__init__(msg)


class TransactionCannotChangeToReconciledException(Exception):
    """Exception raised when is not possible to
    change transaction status to reconciled"""

    def __init__(self, message: str = ""):
        msg = f"Error: Both income_account and expense_account must be filled.{message}"  # noqa
        super().__init__(msg)


class TransactionCannotBeUpdateDueToReconciliationStatus(Exception):
    """Exception raised when is not possible to update the given transaction"""

    def __init__(self, message: str = ""):
        msg = f"Error: Cannot edit transaction, associated reconciliation is not ACTIVE.{message}"  # noqa
        super().__init__(msg)


class LinkedInvoiceNotFoundException(Exception):
    """Exception raised when LinkedInvoicewas not found"""

    def __init__(self, message: str = ""):
        msg = f"Error: LinkedInvoice ID not found.{message}"
        super().__init__(msg)


class TransactionMetadataUpdateError(Exception):
    """Exception raised when there is an error updating transaction metadata"""

    def __init__(self, message="Erro ao atualizar metadados da transação"):
        super().__init__(message)


class DebitCreditConstraintError(Exception):
    """Exception raised when JournalLine deletion violates DEBIT/CREDIT constraints."""

    def __init__(self, message: str = ""):
        msg = (
            f"Error: Cannot delete this JournalLine. A JournalHeader must contain at "
            f"least one DEBIT and one CREDIT line.{message}"
        )
        super().__init__(msg)


class SameAmountConstraintError(Exception):
    """Exception raised when related JournalLines have mismatched amounts."""

    def __init__(self, message: str = ""):
        msg = (
            "Error: Cannot delete this JournalLine. "
            "Related JournalLines must have the same amount."
            f"{message}"
        )
        super().__init__(msg)


class JournalHeaderNotFoundException(Exception):
    """Exception raised when Journal Header was not found"""

    def __init__(self, message: str):
        msg = f"Error: Journal Header ID not found {message}"
        super().__init__(msg)


class JournalLineNotFoundException(Exception):
    """Exception raised when Journal Line was not found"""

    def __init__(self, message: str):
        msg = f"Error: Journal Line ID not found {message}"
        super().__init__(msg)


class JournalLineDeletionError(Exception):
    """Exception raised when deletion of a Journal Line fails"""

    def __init__(self, message: str):
        super().__init__(f"Error: Failed to delete Journal Line: {message}")


class JournalLineInsertError(Exception):
    """Exception raised when insertion of a Journal Line fails"""

    def __init__(self, message: str):
        super().__init__(f"Error: Failed to insert Journal Line: {message}")


class JournalOperationNotAllowedDueToReconciliationStatus(Exception):
    """
    Exception raised when journal operations are not allowed due
    to reconciliation status
    """

    def __init__(self, operation: str, current_status: str):
        operations_map = {
            "insert": "create journal lines",
            "update": "update journal lines",
            "delete": "delete journal lines",
        }
        msg = (
            f"Error: Cannot {operations_map.get(operation, 'perform operation')}. "
            f"Associated reconciliation must be ACTIVE. "
            f"Current status: {current_status}"
        )
        super().__init__(msg)


class JournalLineBatchUpdateError(Exception):
    """Raised when there is an error processing a batch of journal line updates"""

    def __init__(self, message="Error processing bulk journal line update"):
        super().__init__(message)


class AccountConfigurationNotFoundException(Exception):
    """Exception raised when account configuration was not found"""

    def __init__(self, message: str = ""):
        msg = f"Error: Account Configuration not found. {message}"
        super().__init__(msg)


class MultipleChartOfAccountsAccountConfigurationError(Exception):
    """Exception raised when multiple chart of accounts were found"""

    def __init__(self, message: str = ""):
        msg = (
            f"Error: Multiple Chart Of Accounts Account Configuration Found. {message}"
        )
        super().__init__(msg)


class AccountNotFoundException(Exception):
    """Exception raised when it's not acceptable
    to change reconciliation status"""

    def __init__(self):
        msg = """
        Error: List of accounts not found for a given CNPJ
        """
        super().__init__(msg)


class BankAccountStatusNotFoundException(Exception):
    """Exception raised when it's not acceptable
    to change reconciliation bank status"""

    def __init__(self):
        msg = """
        Error: Could not change bank account status
        """
        super().__init__(msg)


class BankAccountGuidNotFoundException(Exception):
    """Exception raised when bank account guid was not found"""

    def __init__(self, message: str = ""):
        msg = f"Error: Bank account guid not found.{message}"
        super().__init__(msg)


class StatementUploadException(Exception):
    """Exception raised when we are unable to upload file"""

    def __init__(self, message: str = ""):
        msg = f"Error: Could not upload file. {message}"
        super().__init__(msg)


class ReconciliationFileUploadStatusException(Exception):
    """Exception raised when we are unable to change upload file status"""

    def __init__(self, message: str = ""):
        msg = f"Error: Could not change file upload status. {message}"
        super().__init__(msg)


class ReconciliationFileUploadValidateException(Exception):
    """Exception raised when upload API returns validation errors"""

    def __init__(self, errors: list):
        self.total_errors = 0
        self.errors = []

        for item in errors:
            row = item.get("row")
            for error in item.get("errors", []):
                self.total_errors += 1
                field = error.get("field")
                error_msg = error.get("error")
                self.errors.append(f"Linha {row}, Coluna '{field}': {error_msg}")

        self.message = (
            "Erro: A planilha não pode ser processada devido a erros de validação."
        )
        super().__init__(self.message)

    def to_dict(self):
        return {
            "message": self.message,
            "total_errors": self.total_errors,
            "errors": self.errors,
        }


class SuggestedInvoiceNotFoundException(Exception):
    """Exception raised when a SuggestedInvoice record was not found"""

    def __init__(self, message: str = ""):
        msg = f"Error: SuggestedInvoice not found.{message}"
        super().__init__(msg)


class ReconciliationFileUploadNotFoundException(Exception):
    """Exception raised when a ReconciliationFileUpload record was not found"""

    def __init__(self, message: str = ""):
        msg = f"Error: Could not find upload file.{message}"
        super().__init__(msg)
