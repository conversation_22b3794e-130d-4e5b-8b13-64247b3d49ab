from abc import ABC, abstractmethod
from typing import Dict, Generator, List, Optional, Tuple

from domain.models import (
    Account,
    AccountConfiguration,
    BankAccountStatus,
    FileUploadStatus,
    JournalHeader,
    JournalLine,
    Reconciliation,
    ReconciliationFileUpload,
    ReconciliationStatusEnum, Invoice,
)


class AbstractUploadRepository(ABC):
    @abstractmethod
    def create_upload(self, upload_data: Dict):
        pass

    @abstractmethod
    def get_latest_uploaded_file(self, upload_id: str) -> ReconciliationFileUpload:
        pass

    @abstractmethod
    def set_status(
        self,
        reconciliation_file_upload: ReconciliationFileUpload,
        status: FileUploadStatus,
        processed_at: Optional[str] = None,
        error_message: Optional[str] = None,
        sheets_url: Optional[str] = None,
    ):
        pass


class AbstractJournalRepository(ABC):
    @abstractmethod
    def get_journal_header(self, transaction_id: int):
        pass

    @abstractmethod
    def set_journal_status_to_reconciled(self, transaction: JournalHeader):
        pass

    @abstractmethod
    def set_journal_status_to_new(self, transaction: JournalHeader):
        pass

    @abstractmethod
    def set_journal_account_based_on_chart_of_accounts(
        self, bank_code: str, reconciliation_id: str
    ):
        pass

    @abstractmethod
    def delete_journal_line(self, journal_line: JournalLine):
        pass

    @abstractmethod
    def insert_journal_line(self, journal_line: JournalLine) -> JournalLine:
        pass

    @abstractmethod
    def get_journal_lines_by_ids(
        self, journal_line_ids: List[str]
    ) -> List[JournalLine]:
        pass


class AbstractMatchRepository(ABC):
    @abstractmethod
    def get_by_platform_id(
        self, platform_transaction_id: str, platform_invoice_id: str
    ):
        pass

    @abstractmethod
    def create_or_update(self, matches: list):
        pass

    @abstractmethod
    def count_by_transaction_ids(self, transaction_ids: List[str]) -> Dict[str, int]:
        pass


class AbstractReconciliationRepository(ABC):
    @abstractmethod
    def set_status(
        self, reconciliation: Reconciliation, status: ReconciliationStatusEnum
    ):
        pass

    @abstractmethod
    def set_bank_status(
        self,
        reconciliation: Reconciliation,
        status_bank_account_guids: Dict[str, BankAccountStatus],
    ):
        pass

    @abstractmethod
    def get_reconciliation(self, id: int):
        pass

    @abstractmethod
    def get_reconciliations_by_customer_and_competence(
        self, cockpit_customer_id: str, competence: str
    ):
        pass

    @abstractmethod
    def check_all_journal_headers_have_suggestions(
        self,
        reconciliation: Reconciliation,
        bankaccount_guid: str,
        match_repository: AbstractMatchRepository,
    ) -> bool:
        pass


class AbstractAccountRepository(ABC):
    @abstractmethod
    def create_or_update(self, accounts: list):
        pass

    @abstractmethod
    def get_accounts_by_cnpj(self, cnpj: str) -> List[Account]:
        pass

    @abstractmethod
    def get_accounts_by_cnpj_ordered(self, cnpj: str) -> List[dict]:
        pass

    @abstractmethod
    def get_analytic_accounts_by_cnpj(self, cnpj: str) -> List[Account]:
        pass


class AbstractPlatformTransactionRepository(ABC):
    @abstractmethod
    def get_transactions_by_bankaccounts(
        bank_account_guids: str,
        invoices: List[Invoice],
        competence: str,
    ) -> Tuple[List[JournalHeader], List[dict]]:
        pass


class AbstractLinkedInvoiceRepository(ABC):
    @abstractmethod
    def get_linkedinvoice(self, platform_invoice_id: str):
        pass

    @abstractmethod
    def get_invoices_from_finished_reconciliations(
        self, platform_invoice_ids: List[str]
    ) -> List[str]:
        pass


class AbstractPartnerEntityRepository(ABC):
    @abstractmethod
    def get_by_cpf_cnpj(self, cpf_cnpj: str):
        pass

    @abstractmethod
    def get_by_cpf_cnpj_list(self, cpf_cnpj_list: list[str]):
        pass


class AbstractAccountConfigurationRepository(ABC):
    @abstractmethod
    def create(self, cockpit_account_id: str, bank_code: str):
        pass

    @abstractmethod
    def get_by_reconciliation_and_account(
        self, reconciliation_id: str, cockpit_account_id: str
    ):
        pass

    @abstractmethod
    def update(self, configuration: AccountConfiguration):
        pass

    @abstractmethod
    def get_account_chart_of_accounts_latest_configuration(
        self, cockpit_account_id: str
    ):
        pass


class AbstractInvoiceRepository(ABC):
    @abstractmethod
    def get_by_cnpj(self, taker_document: str, issuer_document: str, competence: str):
        pass
