"""adiciona_nova_tabela_de_invoices

Revision ID: 9227c1c61749
Revises: 33c42ebc5736
Create Date: 2025-06-03 18:14:01.823242

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "9227c1c61749"
down_revision = "33c42ebc5736"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "invoice",
        sa.Column(
            "type",
            sa.Enum("NFCE", "CTE", "NFE", "NFSE", name="invoice_type_enum"),
            nullable=False,
        ),
        sa.Column("number", sa.String(length=255), nullable=False),
        sa.Column(
            "status",
            sa.Enum("ISSUED", "CANCELLED", "DENEGADA", name="invoice_status_enum"),
            nullable=True,
        ),
        sa.Column("issue_date", sa.DateTime(timezone=True), nullable=False),
        sa.Column("competence_date", sa.Date(), nullable=False),
        sa.Column("issuer_name", sa.String(length=255), nullable=False),
        sa.Column("issuer_document", sa.String(length=20), nullable=False),
        sa.Column("taker_name", sa.String(length=255), nullable=False),
        sa.Column("taker_document", sa.String(length=20), nullable=False),
        sa.Column("customer_document", sa.String(length=20), nullable=False),
        sa.Column("amount", sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column("net_amount", sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False
        ),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_invoice_competence_date"), "invoice", ["competence_date"], unique=False
    )
    op.create_index(
        op.f("ix_invoice_customer_document"),
        "invoice",
        ["customer_document"],
        unique=False,
    )
    op.create_index(
        op.f("ix_invoice_issue_date"), "invoice", ["issue_date"], unique=False
    )
    op.create_index(
        "ix_invoice_issue_date_issuer_document",
        "invoice",
        ["issue_date", "issuer_document"],
        unique=False,
    )
    op.create_index(
        "ix_invoice_issue_date_issuer_name",
        "invoice",
        ["issue_date", "issuer_name"],
        unique=False,
    )
    op.create_index(
        "ix_invoice_issue_date_taker_document",
        "invoice",
        ["issue_date", "taker_document"],
        unique=False,
    )
    op.create_index(
        "ix_invoice_issue_date_taker_name",
        "invoice",
        ["issue_date", "taker_name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_invoice_issuer_document"), "invoice", ["issuer_document"], unique=False
    )
    op.create_index(op.f("ix_invoice_number"), "invoice", ["number"], unique=False)
    op.create_index(op.f("ix_invoice_status"), "invoice", ["status"], unique=False)
    op.create_index(
        op.f("ix_invoice_taker_document"), "invoice", ["taker_document"], unique=False
    )
    op.create_index(op.f("ix_invoice_type"), "invoice", ["type"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_invoice_type"), table_name="invoice")
    op.drop_index(op.f("ix_invoice_taker_document"), table_name="invoice")
    op.drop_index(op.f("ix_invoice_status"), table_name="invoice")
    op.drop_index(op.f("ix_invoice_number"), table_name="invoice")
    op.drop_index(op.f("ix_invoice_issuer_document"), table_name="invoice")
    op.drop_index("ix_invoice_issue_date_taker_name", table_name="invoice")
    op.drop_index("ix_invoice_issue_date_taker_document", table_name="invoice")
    op.drop_index("ix_invoice_issue_date_issuer_name", table_name="invoice")
    op.drop_index("ix_invoice_issue_date_issuer_document", table_name="invoice")
    op.drop_index(op.f("ix_invoice_issue_date"), table_name="invoice")
    op.drop_index(op.f("ix_invoice_customer_document"), table_name="invoice")
    op.drop_index(op.f("ix_invoice_competence_date"), table_name="invoice")
    op.drop_table("invoice")
    # ### end Alembic commands ###
