"""Remove transactions

Revision ID: 41861fa36723
Revises: 92f61dd66291
Create Date: 2025-05-27 10:12:43.376869

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "41861fa36723"
down_revision = "92f61dd66291"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "linked_invoice_transaction_id_fkey", "linked_invoice", type_="foreignkey"
    )
    op.drop_column("linked_invoice", "transaction_id")
    op.drop_index("transaction_reconciliation_id_delete_date", table_name="transaction")
    op.drop_table("transaction")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "linked_invoice",
        sa.Column("transaction_id", sa.UUID(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "linked_invoice_transaction_id_fkey",
        "linked_invoice",
        "transaction",
        ["transaction_id"],
        ["id"],
    )
    op.create_table(
        "transaction",
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "cockpit_account_id",
            sa.VARCHAR(length=255),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "platform_transaction_id",
            sa.VARCHAR(length=255),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "amount",
            sa.NUMERIC(precision=10, scale=2),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("date", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column(
            "description", sa.VARCHAR(length=255), autoincrement=False, nullable=True
        ),
        sa.Column("notes", sa.VARCHAR(length=255), autoincrement=False, nullable=True),
        sa.Column(
            "type",
            postgresql.ENUM("INCOME", "EXPENSE", name="transaction_type"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "income_account", sa.VARCHAR(length=255), autoincrement=False, nullable=True
        ),
        sa.Column(
            "expense_account",
            sa.VARCHAR(length=255),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "status",
            postgresql.ENUM(
                "NEW", "RECONCILED", "ADVANCE_RECONCILED", name="transaction_status"
            ),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "is_linked_invoice_applicable",
            sa.BOOLEAN(),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "has_linked_invoices", sa.BOOLEAN(), autoincrement=False, nullable=True
        ),
        sa.Column("reconciliation_id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("supplier_id", sa.UUID(), autoincrement=False, nullable=True),
        sa.Column("customer_id", sa.UUID(), autoincrement=False, nullable=True),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "deleted_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True
        ),
        sa.Column("deleted", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column(
            "transaction_metadata",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["customer_id"], ["partner_entity.id"], name="transaction_customer_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["reconciliation_id"],
            ["reconciliation.id"],
            name="transaction_reconciliation_id_fkey",
        ),
        sa.ForeignKeyConstraint(
            ["supplier_id"], ["partner_entity.id"], name="transaction_supplier_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="transaction_pkey"),
    )
    op.create_index(
        "transaction_reconciliation_id_delete_date",
        "transaction",
        ["reconciliation_id", "deleted", "date"],
        unique=False,
    )
    # ### end Alembic commands ###
