"""
Simplified Categorizer Engine
Implements regex-based account categorization for bank transactions
"""

import re
import time
from dataclasses import dataclass
from typing import Dict, List, Optional

from domain.models import JournalHeader
from shared.logger import get_logger

logger = get_logger()


@dataclass
class CategorizationRule:
    """
    Represents a categorization rule with regex patterns
    """

    name: str
    description_pattern: re.Pattern
    account_search_pattern: re.Pattern
    confidence: float
    transaction_type_filter: Optional[str] = None
    exclude_pattern: Optional[re.Pattern] = None

    def __post_init__(self):
        """Ensure patterns are compiled regex objects"""
        if isinstance(self.description_pattern, str):
            self.description_pattern = re.compile(
                self.description_pattern, re.IGNORECASE
            )
        if isinstance(self.account_search_pattern, str):
            self.account_search_pattern = re.compile(
                self.account_search_pattern, re.IGNORECASE
            )
        if self.exclude_pattern and isinstance(self.exclude_pattern, str):
            self.exclude_pattern = re.compile(self.exclude_pattern, re.IGNORECASE)


class SimplifiedCategorizer:
    """
    Main categorization engine that applies regex rules to suggest accounts
    """

    def __init__(self, accounts):
        self.accounts = accounts
        self.rules = self._initialize_rules()
        self.reset_stats()
        logger.info(f"SimplifiedCategorizer initialized with {len(self.rules)} rules")

    def _initialize_rules(self) -> List[CategorizationRule]:
        """
        Initialize all categorization rules
        Returns rules sorted by confidence (highest first)
        """
        rules = []

        # Rule 1: Tarifas Bancárias (95% confidence)
        rules.append(
            CategorizationRule(
                name="tarifas_bancarias",
                description_pattern=r"^TAR\s|TARIFA|TAR\s+DOC|TAR\s+TED|TAXA\s+BANC",
                account_search_pattern=(
                    r"TARIFA|COMISSOES?\s+BANC|DESPESA\s+BANC|TAXA\s+BANC"
                ),
                confidence=0.95,
                transaction_type_filter="EXPENSE",
            )
        )

        # Rule 2: Telecomunicações (90% confidence)
        rules.append(
            CategorizationRule(
                name="telefone_internet",
                description_pattern=(
                    r"TELEF|INTERNET|\bVIVO\b|\bCLARO\b|\bTIM\b|\bOI\b|"
                    r"\bNEXTEL\b|\bNET\b|\bSKY\b"
                ),
                account_search_pattern=r"TELEFON|INTERNET|COMUNICACAO|TELECOMUNICAC",
                exclude_pattern=r"A\s+PAGAR|RECEBER",
                confidence=0.90,
            )
        )

        # Rule 3: Sindicatos (90% confidence)
        rules.append(
            CategorizationRule(
                name="sindicato",
                description_pattern=r"SIND\s|SINDICATO",
                account_search_pattern=r"SIND|CONTRIBUI.*SIND",
                exclude_pattern=r"RECOLHER|PAGAR\s+SIND|A\s+PAGAR",
                confidence=0.90,
                transaction_type_filter="EXPENSE",
            )
        )

        # Rule 4: PIX Transfers (80% confidence)
        rules.append(
            CategorizationRule(
                name="pix_transfers",
                description_pattern=r"PIX(?:\s|$)|TRANSF.*PIX|ENV.*PIX|REC.*PIX",
                account_search_pattern=r"CLIENTE|FORNECEDOR|TRANSFERENCIA|PIX",
                confidence=0.80,
            )
        )

        # Rule 5: TED/DOC (75% confidence)
        rules.append(
            CategorizationRule(
                name="ted_doc_transfers",
                description_pattern=r"\bTED\b|\bDOC\b|TRANSF.*TED|TRANSF.*DOC",
                account_search_pattern=r"TRANSFERENCIA|BANCO|TED|DOC",
                exclude_pattern=r"^TAR",  # Exclude tariffs
                confidence=0.75,
            )
        )

        # Rule 6: Combustível (85% confidence)
        rules.append(
            CategorizationRule(
                name="combustivel",
                description_pattern=(
                    r"POSTO|GASOLINA|ETANOL|ALCOOL|COMBUSTIVEL|SHELL|"
                    r"PETROBRAS|IPIRANGA|BR\s+MANIA|AUTO\s+POSTO"
                ),
                account_search_pattern=r"COMBUSTIVEL|GASOLINA|VEICULO",
                confidence=0.85,
                transaction_type_filter="EXPENSE",
            )
        )

        # Rule 7: Alimentação (80% confidence)
        rules.append(
            CategorizationRule(
                name="alimentacao",
                description_pattern=(
                    r"RESTAURANTE|PADARIA|LANCHONETE|IFOOD|UBER\s+EATS|"
                    r"RAPPI|DELIVERY|CAFE|PIZZARIA|CHURRASCARIA"
                ),
                account_search_pattern=r"ALIMENTACAO|REFEICAO|RESTAURANTE",
                confidence=0.80,
                transaction_type_filter="EXPENSE",
            )
        )

        # Rule 8: Farmácia (85% confidence)
        rules.append(
            CategorizationRule(
                name="farmacia",
                description_pattern=(
                    r"FARMACIA|MEDICAMENTO|DROGARIA|DROGA\s+RAIA|"
                    r"PACHECO|PAGUE\s+MENOS|DROGASIL"
                ),
                account_search_pattern=r"FARMACIA|MEDICAMENTO|SAUDE",
                confidence=0.85,
                transaction_type_filter="EXPENSE",
            )
        )

        # Rule 9: Impostos (90% confidence)
        rules.append(
            CategorizationRule(
                name="impostos",
                description_pattern=(
                    r"RECEITA|FEDERAL|INSS|IRRF|PIS|COFINS|CSLL|IRPJ|"
                    r"SIMPLES|DARF|ISS|ICMS|FGTS"
                ),
                account_search_pattern=r"IMPOSTO|TRIBUTO|RECEITA|ENCARGO",
                confidence=0.90,
                transaction_type_filter="EXPENSE",
            )
        )

        # Rule 10: Aluguel (90% confidence)
        rules.append(
            CategorizationRule(
                name="aluguel",
                description_pattern=r"ALUGUEL|LOCACAO|CONDOMINIO",
                account_search_pattern=r"ALUGUEL|LOCACAO|IMOVEL",
                exclude_pattern=r"RECEBER",
                confidence=0.90,
            )
        )

        # Rule 11: Utilidades (85% confidence)
        rules.append(
            CategorizationRule(
                name="utilidades",
                description_pattern=(
                    r"ENERGIA|AGUA|COPEL|CEMIG|LIGHT|SABESP|SANEPAR|"
                    r"CPFL|ELETROPAULO|CELESC|COELBA"
                ),
                account_search_pattern=r"ENERGIA|AGUA|LUZ|ELETRIC|UTILIDADE",
                confidence=0.85,
                transaction_type_filter="EXPENSE",
            )
        )

        return sorted(rules, key=lambda r: r.confidence, reverse=True)

    def reset_stats(self):
        """Reset statistics for new processing batch"""
        self._stats = {
            "total_processed": 0,
            "total_categorized": 0,
            "by_rule": {},
            "errors": 0,
            "processing_time_ms": 0,
            "cache_hits": 0,
            "cache_misses": 0,
        }

    def get_stats(self) -> Dict:
        """Get current categorization statistics"""
        return self._stats.copy()

    def categorize_journal(self, journal_header: JournalHeader) -> Optional[Dict]:
        """
        Categorize a JournalHeader and return account suggestion

        Args:
            journal_header: JournalHeader object with transaction data
            cnpj: Company CNPJ for account lookup

        Returns:
            Dict with categorization result or None if no match
        """
        start_time = time.time()
        self._stats["total_processed"] += 1

        try:
            # Validate inputs
            if not journal_header.description:
                return None

            # Apply rules in order of confidence
            for rule in self.rules:
                if self._match_rule(
                    rule, journal_header.description, journal_header.type
                ):
                    account = self._find_matching_account(rule, journal_header.type)
                    if account:
                        self._stats["total_categorized"] += 1
                        self._stats["by_rule"][rule.name] = (
                            self._stats["by_rule"].get(rule.name, 0) + 1
                        )

                        result = {
                            "account_id": str(account.id),
                            "account_code": str(account.short_code),
                            "account_description": account.description,
                            "classification": account.classification,
                            "rule_name": rule.name,
                            "confidence": rule.confidence,
                        }

                        logger.debug(
                            f"Categorized '{journal_header.description}' using rule '"
                            f"{rule.name}' -> {account.description}"
                        )
                        return result

            return None

        except Exception as e:
            logger.error(
                f"Error categorizing journal {journal_header.source_reference_id}: {e}"
            )
            self._stats["errors"] += 1
            return None
        finally:
            elapsed_ms = (time.time() - start_time) * 1000
            self._stats["processing_time_ms"] += elapsed_ms

    def _match_rule(
        self, rule: CategorizationRule, description: str, transaction_type: str
    ) -> bool:
        """
        Check if a rule matches the transaction
        """
        # Check transaction type filter
        if (
            rule.transaction_type_filter
            and rule.transaction_type_filter != transaction_type
        ):
            return False

        # Check description pattern
        if not rule.description_pattern.search(description):
            return False

        # Check exclude pattern
        if rule.exclude_pattern and rule.exclude_pattern.search(description):
            return False

        return True

    def _find_matching_account(self, rule: CategorizationRule, transaction_type: str):
        """
        Find an account that matches the rule's search pattern
        """
        # Filter by operation type based on transaction type
        expected_operation = "DEBIT" if transaction_type == "EXPENSE" else "CREDIT"

        for account in self.accounts:
            # Check operation type matches
            if account.operation_type != expected_operation:
                continue

            # Check if account description matches the search pattern
            if rule.account_search_pattern.search(account.description):
                self._stats["cache_hits"] += 1
                return account

        return None
