"""
Dynamic Categorizer Engine
"""

import re
from difflib import <PERSON>quence<PERSON>atch<PERSON>
from typing import Dict, Generator, List, Tuple

import unidecode

from application.sim_fuzzy import name_similarity_fuzzy
from domain.models import JournalHeader
from shared.logger import get_logger

logger = get_logger()


class InvoiceCategorizer:
    """
    Main categorization engine that applies regex rules to suggest accounts
    """

    def __init__(self, accounts):
        self.accounts = accounts
        self.reset_stats()

    def reset_stats(self):
        """Reset statistics for new processing batch"""
        self._stats = {
            "total_processed": 0,
            "total_categorized": 0,
            "by_rule": {},
            "errors": 0,
            "processing_time_ms": 0,
            "cache_hits": 0,
            "cache_misses": 0,
        }

    def get_stats(self) -> Dict:
        """Get current categorization statistics"""
        return self._stats.copy()

    def __string_normalizer(self, s: str) -> str:
        """
        Normalize a string for comparison:
        - Converts to lowercase.
        - Removes accents (using unidecode).
        - Strips leading/trailing whitespace.
        Used to standardize names and documents before similarity calculation.
        """
        if not s or not isinstance(s, str):
            return ""
        return unidecode.unidecode(s.lower().strip())

    def __cnpj_normalizer(self, cnpj: str) -> str:
        """
        Normalize a CNPJ/CPF:
        - Removes all non-numeric characters.
        - Strips leading zeros.
        Ensures different CNPJ/CPF formats are comparable.
        """
        if not cnpj or not isinstance(cnpj, str):
            return ""
        return re.sub(r"\D", "", cnpj).lstrip("0")

    def __compute_string_similarity(self, string_a: str, string_b: str) -> float:
        """
        Compute similarity between two normalized strings using SequenceMatcher.
        Returns 1.0 for identical strings, or a score from 0 to 1 based on similarity.
        Used for comparing documents (CNPJ/CPF) and other textual fields.
        """
        string_a = self.__string_normalizer(string_a)
        string_b = self.__string_normalizer(string_b)
        if string_a == string_b:
            return 1.0
        return SequenceMatcher(None, string_a, string_b).ratio()

    def __compute_amount_similarity(self, amount_a: float, amount_b: float) -> float:
        """
        Compute similarity between two numeric values (financial amounts):
        - Uses absolute value to allow matching between debit/credit.
        - Score 1.0 for identical values, decreasing linearly as the relative
        difference increases.
        """
        if amount_a == 0 or amount_b == 0:
            return 0.0
        return 1 - abs(abs(amount_a) - abs(amount_b)) / max(
            abs(amount_a), abs(amount_b)
        )

    def __extract_transaction_cnpj_name(
        self, journal_header: JournalHeader
    ) -> Tuple[str, str]:
        """
        Extracts the relevant CNPJ/CPF and name from a financial transaction.
        Looks for the most probable fields (supplier, trading_entity, etc.).
        Ensures reconciliation uses the correct data for matching.
        """
        cnpj = journal_header.partner_entity.cpf_cnpj or ""
        name = journal_header.partner_entity.name or ""

        return self.__cnpj_normalizer(str(cnpj)), name

    def __extract_invoice_cnpj_name(self, invoice: dict) -> Tuple[str, str]:
        """
        Extracts the relevant CNPJ/CPF and name from an invoice.
        Looks for the most probable fields (issuerDocument, customerDocument, etc.).
        Ensures reconciliation uses the correct data for matching.
        """
        cnpj = (
            invoice.get("issuerDocument")
            or invoice.get("customerDocument")
            or invoice.get("takerDocument")
            or ""
        )
        name = (
            invoice.get("issuerName")
            or invoice.get("customerName")
            or invoice.get("takerName")
            or invoice.get("providerName")
            or ""
        )
        return self.__cnpj_normalizer(str(cnpj)), name

    def __extract_invoice_amount(self, invoice: dict) -> float:
        """
        Extracts the relevant amount from an invoice:
        - Prioritizes netAmount if available.
        - Uses gross amount (amount) as fallback.
        Essential for accurate financial reconciliation, considering discounts
         and withholdings.
        """
        net = invoice.get("netAmount")
        if net is not None:
            try:
                return float(net)
            except Exception:
                pass
        amt = invoice.get("amount")
        if amt is not None:
            try:
                return float(amt)
            except Exception:
                pass
        return 0.0

    def calculate_score(self, journal_header: JournalHeader, invoice: dict) -> float:
        """
        Calculates the similarity score between a transaction and an invoice:
        - Considers amount, name, and document.
        - Strongly penalizes if name and document do not match.
        - Only returns maximum score if both name and document are highly similar.
        - Prevents false positives in generic names or matches by CNPJ only.
        Returns a score from 0 to 1, reflecting the confidence in the match.
        """
        self._stats["total_processed"] += 1

        value_trans = float(journal_header.amount or 0)
        value_invoice = self.__extract_invoice_amount(invoice)
        score_value = self.__compute_amount_similarity(value_trans, value_invoice)
        partner_entity_cnpj, partner_entity_name = self.__extract_transaction_cnpj_name(
            journal_header
        )
        invoice_cnpj, invoice_name = self.__extract_invoice_cnpj_name(invoice)
        score_name = name_similarity_fuzzy(partner_entity_name, invoice_name)
        score_doc = self.__compute_string_similarity(partner_entity_cnpj, invoice_cnpj)

        # Novo: se documento e valor batem perfeitamente, sugere independente do nome
        if score_doc >= 0.98 and abs(abs(value_trans) - abs(value_invoice)) < 1e-2:
            logger.warning(
                f"""FORCE MATCH DOC: score_name={score_name:.2f} |
                 score_doc={score_doc:.2f} | score_value={score_value:.2f} |
                  transaction={value_trans} | invoice={value_invoice}"""
            )
            return 0.98

        if score_name < 0.5 and score_doc < 0.8:
            return 0.0
        if score_doc < 0.8:
            score_name = min(score_name, 0.3)
        if abs(abs(value_trans) - abs(value_invoice)) < 1e-2:
            logger.warning(
                f"""Transaction amount less than invoice amount: 
                score_name={score_name:.2f} | score_doc={score_doc:.2f}
                 | score_value={score_value:.2f} | 
                 transaction={value_trans} | invoice={value_invoice}"""
            )
            if score_name >= 0.75 and score_doc >= 0.9:
                return 0.98
        if score_value < 0.7:
            return score_value * max(score_name, score_doc)
        if score_name < 0.5 and score_doc < 0.5:
            return 0.0
        if max(score_name, score_doc) < 0.5:
            return max(score_name, score_doc) * 0.5
        if score_name < 0.5:
            return score_name * 0.5

        return 0.7 * max(score_name, score_doc) + 0.3 * score_value

    def suggest_invoices_for_transaction(
        self,
        journal_header: JournalHeader,
        invoices: Generator,
        top_n: int = 3,
    ) -> List[Tuple[dict, float]]:
        """
        Suggests the best invoices for a financial transaction:
        - Calculates the similarity score for each invoice.
        - Returns the top N suggestions with score above the threshold.
        - Orders by descending score.
        Enables automated, transparent, and auditable reconciliation.
        """
        scored = [
            (invoice, self.calculate_score(journal_header, invoice))
            for invoice in invoices
        ]
        scored = [x for x in scored if x[1] > 0.5]
        scored.sort(key=lambda x: x[1], reverse=True)
        return scored[:top_n]
