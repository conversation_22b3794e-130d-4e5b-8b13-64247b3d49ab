import time
import urllib.parse
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
from itertools import chain
from typing import Dict, List

import requests

from config.config import settings
from shared.logger import get_logger

logger = get_logger()

GET_INVOICES_CHUNK_SIZE = 200

# Constants for ThreadPoolExecutor configuration
MAX_WORKERS = 2
TIMEOUT_SECONDS = 60


class InvoicesApiClient:
    """Chupacabra API v2 Client"""

    def __init__(self):
        self.base_url = settings.INVOICES_API_V2_BASEURL
        self.cockpit_auth_url = f"{settings.AUTH0_ISSUER}oauth/token"
        self.application_json = "application/json"
        self.headers = {
            "accept": self.application_json,
            "content-type": self.application_json,
            "cache-control": "no-cache",
        }

    def get_invoices(
        self,
        issuer_document: str,
        start_date: str,
        end_date: str,
        taker_document: str = None,
        offset: int = 0,
    ) -> dict:
        """Get list of invoices by document and date range"""
        params = {
            "issuer_document": issuer_document,
            "start_date": start_date,
            "end_date": end_date,
            "offset": offset,
        }
        if taker_document:
            params["taker_document"] = taker_document

        return self.__make_request("/api/v1/invoices", params)

    def get_invoices_by_ids(self, ids: List[str], offset: int = 0) -> dict:
        """Get list of invoices by ID list"""
        if not ids:
            return []

        def create_chunks(ids, size=GET_INVOICES_CHUNK_SIZE):
            filtered_ids = [id for id in ids if id is not None]
            return [
                filtered_ids[i : i + size]  # noqa
                for i in range(0, len(filtered_ids), size)
            ]

        def create_params(chunks):
            return [
                (
                    "/api/v1/invoices",
                    {"ids": ",".join(map(str, chunk)), "offset": 0},
                    False,
                )
                for chunk in chunks
            ]

        def make_request(endpoint, params, clear_cache):
            response = self.__make_request(endpoint, params, clear_cache)
            return response.get("data")

        chunks = create_chunks(ids)

        if not chunks:
            return []

        data = create_params(chunks)

        with ThreadPoolExecutor() as executor:
            results = executor.map(lambda args: make_request(*args), data)

        self.__get_bearer_token.cache_clear()
        return list(chain.from_iterable(results))

    @lru_cache(maxsize=1)
    def __get_bearer_token(self) -> str:
        """Get bearer token from auth service"""
        try:
            payload = {
                "client_id": settings.INTEGRADOR_NF_CLIENT_ID,
                "client_secret": settings.INTEGRADOR_NF_CLIENT_SECRET,
                "audience": settings.INTEGRADOR_NF_AUDIENCE,
                "grant_type": "client_credentials",
            }
            response = requests.post(
                url=self.cockpit_auth_url,
                headers={
                    "accept": self.application_json,
                    "content-type": self.application_json,
                },
                json=payload,
            )
            response.raise_for_status()
            return f"Bearer {response.json().get('access_token')}"
        except requests.exceptions.RequestException as exc:
            logger.error(f"Error getting bearer token: {str(exc)}")
            raise

    def __make_request(self, endpoint: str, params: Dict, cache_clear=True) -> dict:
        """Make authenticated API request"""
        self.headers["Authorization"] = self.__get_bearer_token()

        url = f"{self.base_url}{endpoint}?{urllib.parse.urlencode(params)}"
        logger.info(f"Calling {url}")

        start = time.time()
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as exc:
            logger.error(f"Error requesting {exc.request.url!r}")
            logger.error(exc.response.json())
            raise
        finally:
            total_time = time.time() - start
            if cache_clear:
                self.__get_bearer_token.cache_clear()
            logger.info(f"Request time: {total_time:.4f} seconds")
