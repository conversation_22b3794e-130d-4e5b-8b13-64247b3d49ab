from typing import List

from sqlalchemy import and_
from sqlalchemy.exc import NoResultFound

from domain.exceptions import LinkedInvoiceNotFoundException
from domain.models import (
    JournalHeader,
    JournalLine,
    LinkedInvoice,
    Reconciliation,
    ReconciliationStatusEnum,
)
from domain.repositories import AbstractLinkedInvoiceRepository
from shared.logger import get_logger
from shared.session_manager import session

logger = get_logger()


class LinkedInvoiceRepository(AbstractLinkedInvoiceRepository):
    def __init__(self) -> None:
        self.session = session
        super().__init__()

    def get_linkedinvoice(self, platform_invoice_id: str):
        try:
            linked_invoice = LinkedInvoice.get(platform_invoice_id)
            return linked_invoice
        except NoResultFound:
            raise LinkedInvoiceNotFoundException(
                message=f"LinkedInvoice ID {platform_invoice_id}"
            )
        except Exception:
            return None

    def execute_raw_query(self, query: str):
        try:
            result = self.session.execute(query)
            return result.fetchall()
        except Exception as e:
            logger.exception(f"An error occurred: {e}")
            return None

    def get_invoices_from_finished_reconciliations(
        self, platform_invoice_ids: List[str], cockpit_customer_id: str = None
    ) -> List[str]:
        try:
            filters = [
                LinkedInvoice.platform_invoice_id.in_(platform_invoice_ids),
                Reconciliation.status == ReconciliationStatusEnum.FINISHED,
                Reconciliation.deleted == False,  # noqa
                JournalHeader.deleted == False,  # noqa
                JournalLine.deleted == False,  # noqa
                LinkedInvoice.deleted == False,  # noqa
            ]
            if cockpit_customer_id:
                filters.append(
                    Reconciliation.cockpit_customer_id == cockpit_customer_id
                )

            result = (
                self.session.query(LinkedInvoice.platform_invoice_id)
                .join(JournalLine, LinkedInvoice.journal_line_id == JournalLine.id)
                .join(JournalHeader, JournalLine.id == JournalHeader.id)
                .join(
                    Reconciliation, JournalHeader.reconciliation_id == Reconciliation.id
                )
                .filter(and_(*filters))
                .distinct()
                .all()
            )
            return [invoice[0] for invoice in result]
        except Exception as e:
            logger.exception(f"Error getting linked invoice IDs: {e}")
            return []
