from typing import List

from sqlalchemy import select, text

from domain.models import Account
from domain.repositories import AbstractAccountRepository
from shared.session_manager import session_scope


class AccountRepository(AbstractAccountRepository):
    def __init__(self) -> None:
        super().__init__()

    def create_or_update(self, accounts: List[Account]):
        with session_scope() as session:
            session.add_all(accounts)
            session.commit()
        return accounts

    def get_analytic_accounts_by_cnpj(self, cnpj: str) -> List[Account]:
        """
        Returns only ANALYTIC accounts for a specific CNPJ.
        Used by SimplifiedCategorizer for account matching.

        :param cnpj: The CNPJ to filter accounts
        :return: List of Account objects (ANALYTIC type only)
        """
        with session_scope(close=False) as session:
            stmt = (
                select(Account)
                .where(Account.cnpj == cnpj)
                .where(Account.account_type == "ANALYTIC")
                .where(Account.status == "ACTIVE")
                .order_by(Account.classification)
            )
            result = session.execute(stmt)
            accounts = result.scalars().all()

        return accounts

    def get_accounts_by_cnpj(self, cnpj: str) -> List[Account]:
        """
        Returns all accounts associated with a specific CNPJ.

        :param cnpj: The CNPJ to filter the accounts.
        :return: A list of dict containing the same properties of Account model
        """

        with session_scope() as session:
            stmt = select(Account).where(Account.cnpj == cnpj)
            result = session.execute(stmt)
            accounts = result.scalars().all()

        return accounts

    def get_accounts_by_cnpj_ordered(self, cnpj: str) -> List[Account]:
        """
        Returns all accounts associated with a specific CNPJ.
        This is a ordered query

        :param cnpj: The CNPJ to filter the accounts.
        :return: A list of dict containing the same properties of Account model
        """

        with session_scope() as session:
            stmt = text(
                """
                SELECT * FROM account
                WHERE cnpj = :cnpj
                ORDER BY (
                    SELECT array_agg(num ORDER BY ord)
                    FROM unnest(string_to_array(classification, '.')::int[])
                    WITH ORDINALITY AS t(num, ord)
                )
            """
            )
            result = session.execute(stmt, {"cnpj": cnpj})
            accounts = [dict(row._mapping) for row in result.fetchall()]

        return accounts
