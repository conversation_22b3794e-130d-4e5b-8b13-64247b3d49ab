from sqlalchemy.exc import NoResultFound

from domain.exceptions import SuggestedInvoiceNotFoundException
from domain.models import SuggestedInvoice
from domain.repositories import AbstractSuggestedInvoiceRepository
from shared.auth import Session
from shared.logger import get_logger

logger = get_logger()


class SuggestedInvoiceRepository(AbstractSuggestedInvoiceRepository):
    def __init__(self, session: Session) -> None:
        super().__init__()
        self.session = session

    def get_by_account_id(self, cockpit_account_id: str):
        try:
            suggested_invoice = SuggestedInvoice.filter_by(cockpit_account_id)
            return suggested_invoice
        except NoResultFound:
            raise SuggestedInvoiceNotFoundException(
                message=f"for account ID {cockpit_account_id}"
            )
        except Exception:
            return None
