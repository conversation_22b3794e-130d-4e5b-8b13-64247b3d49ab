import json
from datetime import datetime

from infrastructure.repositories.s3_repository import S3Repository
from shared.utils.conversor import convert_to_suggested_invoice


class MatchS3Repository(S3Repository):
    def get_suggested_invoices(self, file_path):
        file = super().get_object(file_path)
        matches = json.loads(file)
        sorted_matches = sorted(
            matches,
            key=lambda x: datetime.strptime(
                x["_generation_timestamp"], "%Y-%m-%d %H:%M:%S.%f"
            ),
            reverse=True,
        )
        matches_set = set()

        suggested_invoices = []
        for match in sorted_matches:
            new_suggestions = convert_to_suggested_invoice(match)

            for suggestion in new_suggestions:
                key = (
                    suggestion.platform_transaction_id,
                    suggestion.platform_invoice_id,
                    suggestion.suggestion_type,
                )
                if key in matches_set:
                    continue
                suggested_invoices.append(suggestion)
                matches_set.add(key)

        return suggested_invoices
