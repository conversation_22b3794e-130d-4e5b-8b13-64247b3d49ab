import enum
import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, <PERSON><PERSON>

from application import transaction_service
from application.category_service import CategoryService
from domain.models import (
    AccountConfiguration,
    JournalHeader,
    JournalHeaderStatusEnum,
    JournalLine,
    OperationTypeEnum,
    SuggestedInvoice,
    TransactionTypeEnum, Invoice,
)
from domain.repositories import (
    AbstractAccountConfigurationRepository,
    AbstractPartnerEntityRepository,
    AbstractPlatformTransactionRepository,
)
from infrastructure.transactions_api_client import TransactionsApiClient
from shared.auth import Session
from shared.logger import get_logger
from shared.utils.date import get_start_and_end_of_month
from shared.utils.json import safe_get

logger = get_logger()

# Constants for ThreadPoolExecutor configuration
MAX_WORKERS = 5
TIMEOUT_SECONDS = 60


class PartnerTypeEnum(str, enum.Enum):
    SUPPLIER = "SUPPLIER"
    CUSTOMER = "CUSTOMER"


class PlatformTransactionRepository(AbstractPlatformTransactionRepository):
    def __init__(
        self,
        session: Session,
        partner_repository: AbstractPartnerEntityRepository,
        account_configuration_repository: AbstractAccountConfigurationRepository,
    ) -> None:
        super().__init__()
        self.session = session
        self.partner_repository = partner_repository
        self.account_configuration_repository = account_configuration_repository

    def get_transactions_by_bankaccounts(
        self,
        bank_account_guids: str,
        competence: str,
        invoices: List[Invoice],
        customer_cnpj: str = None,
    ) -> Tuple[List[JournalHeader], List[dict]]:
        from_date, to_date = get_start_and_end_of_month(competence)

        api_client = TransactionsApiClient(self.session)
        bank_accounts = api_client.get_accounts_by_guids(bank_account_guids)

        journal_headers = []
        latest_account_configuration_list = []

        category_service = CategoryService()
        category_service.initialize_for_customer(customer_cnpj)

        # Deduplicate accounts by ID
        bank_accounts_to_process = list(
            {
                bank_account.get("id"): bank_account for bank_account in bank_accounts
            }.values()
        )

        logging.info(f"Total accounts from API: {len(bank_accounts)}")
        logging.info(f"Unique accounts to process: {len(bank_accounts_to_process)}")
        logging.info(
            f"Duplicates filtered: {len(bank_accounts) - len(bank_accounts_to_process)}"
        )

        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            future_to_account = {
                executor.submit(
                    self._process_account,
                    api_client,
                    bank_account,
                    from_date,
                    to_date,
                    category_service,
                    invoices,
                ): bank_account
                for bank_account in bank_accounts_to_process
            }

            for future in as_completed(future_to_account):
                account = future_to_account[future]
                try:
                    result = future.result(timeout=TIMEOUT_SECONDS)

                    journal_headers.extend(result["journal_headers"])
                    if result["account_configuration"]:
                        latest_account_configuration_list.append(
                            result["account_configuration"]
                        )

                except Exception as e:
                    logging.error(f"Error processing account {account.get('id')}: {e}")

        category_service.finalize()

        return journal_headers, latest_account_configuration_list

    def _process_account(
        self,
        api_client: TransactionsApiClient,
        account: dict,
        from_date: str,
        to_date: str,
        category_service: CategoryService,
        invoices: list[SuggestedInvoice],
    ) -> dict:
        """Process a single account - now with cleaner categorization"""
        cockpit_account_id = account.get("bank_account_guid")
        latest_account_configuration = self.account_configuration_repository.get_account_chart_of_accounts_latest_configuration(  # noqa
            cockpit_account_id
        )

        account_config_data = None
        if latest_account_configuration is not None:
            account_config_data = {
                "cockpit_account_id": cockpit_account_id,
                "value": latest_account_configuration.value,
            }

        transactions_data = api_client.get_transactions(
            account_id=account.get("id"), from_date=from_date, to_date=to_date
        )

        journal_headers = []
        for data in transactions_data:
            journal_header = self.__convert_to_journal(
                data, cockpit_account_id, latest_account_configuration
            )
            journal_header = self.__add_partner_information_to_journal(
                data, journal_header
            )

            category_service.categorize_invoice(
                journal_header=journal_header, invoices=invoices
            )

            # Clean categorization call
            category_service.categorize_journal(journal_header)

            journal_headers.append(journal_header)

        return {
            "journal_headers": journal_headers,
            "account_configuration": account_config_data,
        }

    def __add_partner_information_to_journal(self, data, journal_header):
        if not data.get("paymentData"):
            return journal_header

        if journal_header.amount > 0:  # INCOME
            name = safe_get(data, ["paymentData", "payer", "name"])
            cpf_cnpj = safe_get(
                data, ["paymentData", "payer", "documentNumber", "value"]
            )
            partner_type = PartnerTypeEnum.CUSTOMER
            payment_data = safe_get(data, ["paymentData", "payer"])

        else:
            name = safe_get(data, ["paymentData", "receiver", "name"])
            cpf_cnpj = safe_get(
                data, ["paymentData", "receiver", "documentNumber", "value"]
            )
            partner_type = PartnerTypeEnum.SUPPLIER
            payment_data = safe_get(data, ["paymentData", "receiver"])

        transaction_service.update_or_create_partner_entity_for_journal(
            cpf_cnpj=cpf_cnpj,
            name=name,
            journal_header=journal_header,
        )

        self.__update_journal_metadata_with_partner(
            journal_header,
            partner_type,
            payment_data,
            name,
            cpf_cnpj,
        )
        return journal_header

    def __update_journal_metadata_with_partner(
        self, journal_header, partner_type, data, name, cpf_cnpj
    ):
        if "originalApiData" not in journal_header._metadata:
            journal_header._metadata["originalApiData"] = {}

        journal_header._metadata["originalApiData"][partner_type] = data

        if partner_type == PartnerTypeEnum.CUSTOMER:
            journal_header._metadata["hasCustomerFromAPI"] = True
            journal_header._metadata["customerFieldsFromAPI"] = {
                "name": bool(name),
                "cpf_cnpj": bool(cpf_cnpj),
            }
        elif partner_type == PartnerTypeEnum.SUPPLIER:
            journal_header._metadata["hasSupplierFromAPI"] = True
            journal_header._metadata["supplierFieldsFromAPI"] = {
                "name": bool(name),
                "cpf_cnpj": bool(cpf_cnpj),
            }

    def __convert_to_journal(
        self,
        data: dict,
        bank_account: str,
        latest_account_configuration: AccountConfiguration,
    ):
        account_configuration_value = (
            latest_account_configuration.value if latest_account_configuration else None
        )

        transaction_type = (
            TransactionTypeEnum.INCOME
            if data.get("transaction_type") == "CREDIT"
            else TransactionTypeEnum.EXPENSE
        )

        debit_account_id = (
            account_configuration_value
            if transaction_type == TransactionTypeEnum.INCOME
            else None
        )
        credit_account_id = (
            account_configuration_value
            if transaction_type == TransactionTypeEnum.EXPENSE
            else None
        )

        debit = JournalLine(
            line_type=OperationTypeEnum.DEBIT,
            account_id=debit_account_id,
            amount=abs(data.get("amount")),
            description=data.get("description"),
            _metadata={},
        )

        credit = JournalLine(
            line_type=OperationTypeEnum.CREDIT,
            account_id=credit_account_id,
            amount=abs(data.get("amount")),
            description=data.get("description"),
            _metadata={},
        )

        journal_header = JournalHeader(
            amount=data.get("amount"),
            date=data.get("date"),
            description=data.get("description"),
            status=JournalHeaderStatusEnum.NEW,
            cockpit_account_id=bank_account,
            source_reference_id=data.get("id"),
            journal_lines=[debit, credit],
            _metadata={
                "is_linked_invoice_applicable": None,
                "has_linked_invoices": None,
            },
        )

        return journal_header
