from domain.exceptions import InvoiceNotFoundException
from domain.models import Invoice
from domain.repositories import AbstractInvoiceRepository
from shared.auth import Session
from shared.logger import get_logger
from shared.utils.date import get_start_and_end_of_month

logger = get_logger()


class InvoiceRepository(AbstractInvoiceRepository):
    def __init__(self, session: Session) -> None:
        super().__init__()
        self.session = session

    def get_by_cnpj(
        self,
        taker_document: str,
        issuer_document: str,
        competence: str,
    ):
        start_of_month, end_of_month = get_start_and_end_of_month(competence)
        try:
            invoices = Invoice.filter_by(
                taker_document=taker_document,
                issuer_document=start_of_month,
                comeptence_date__lte=end_of_month,
            )
            if not invoices:
                raise InvoiceNotFoundException(
                    message=f"for account ID {taker_document}"
                )
            return invoices
        except InvoiceNotFoundException:
            raise
        except Exception:
            return None
