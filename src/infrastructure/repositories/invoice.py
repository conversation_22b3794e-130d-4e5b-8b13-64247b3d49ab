from datetime import date, datetime

from dateutil.relativedelta import relativedelta
from sqlalchemy import or_

from domain.exceptions import InvoiceNotFoundException
from domain.models import Invoice
from domain.repositories import AbstractInvoiceRepository
from shared.auth import Session
from shared.logger import get_logger

logger = get_logger()


class InvoiceRepository(AbstractInvoiceRepository):
    def __init__(self, session: Session) -> None:
        super().__init__()
        self.session = session

    def _validate_documents(self, taker_document: str = None, issuer_document: str = None) -> None:
        """
        Validate that at least one document is provided.

        Args:
            taker_document: Document of the invoice taker (optional)
            issuer_document: Document of the invoice issuer (optional)

        Raises:
            ValueError: When neither taker_document nor issuer_document is provided
        """
        if not taker_document and not issuer_document:
            raise ValueError("Either taker_document or issuer_document must be provided")

    def _calculate_date_range(self, competence: str = None) -> tuple[date, date]:
        """
        Calculate the start and end dates for the 3-month period.

        Args:
            competence: Competence period in YYYY-MM format (optional)

        Returns:
            Tuple of (start_date, end_date) for the 3-month period

        Raises:
            ValueError: When competence date format is invalid
        """
        if competence:
            competence_date = datetime.strptime(competence, "%Y-%m").date()
            end_date = competence_date
        else:
            end_date = date.today()

        start_date = end_date - relativedelta(months=3)

        return start_date, end_date

    def _build_filter_conditions(
        self,
        taker_document: str = None,
        issuer_document: str = None,
        start_date: date = None,
        end_date: date = None
    ) -> list:
        """
        Build the SQLAlchemy filter conditions for documents and dates.

        Args:
            taker_document: Document of the invoice taker (optional)
            issuer_document: Document of the invoice issuer (optional)
            start_date: Start date for competence_date filtering
            end_date: End date for competence_date filtering

        Returns:
            List of SQLAlchemy filter conditions
        """
        filters = []

        document_conditions = []
        if taker_document:
            document_conditions.append(Invoice.taker_document == taker_document)
        if issuer_document:
            document_conditions.append(Invoice.issuer_document == issuer_document)

        if document_conditions:
            filters.append(or_(*document_conditions))

        if start_date:
            filters.append(Invoice.competence_date >= start_date)
        if end_date:
            filters.append(Invoice.competence_date <= end_date)

        return filters

    def _format_not_found_message(self, taker_document: str = None, issuer_document: str = None) -> str:
        """
        Format the error message when no invoices are found.

        Args:
            taker_document: Document of the invoice taker (optional)
            issuer_document: Document of the invoice issuer (optional)

        Returns:
            Formatted error message string
        """
        document_info = []
        if taker_document:
            document_info.append(f"taker_document: {taker_document}")
        if issuer_document:
            document_info.append(f"issuer_document: {issuer_document}")

        return f"for {', '.join(document_info)} within the last 3 months"

    def _log_success(
        self,
        invoices: list,
        taker_document: str = None,
        issuer_document: str = None,
        start_date: date = None,
        end_date: date = None
    ) -> None:
        """
        Handle the success logging for invoice retrieval.

        Args:
            invoices: List of retrieved invoices
            taker_document: Document of the invoice taker (optional)
            issuer_document: Document of the invoice issuer (optional)
            start_date: Start date of the search range
            end_date: End date of the search range
        """
        logger.info(
            f"Found {len(invoices)} invoices for documents "
            f"(taker: {taker_document}, issuer: {issuer_document}) "
            f"between {start_date} and {end_date}"
        )

    def get_by_cnpj(
        self,
        taker_document: str = None,
        issuer_document: str = None,
        competence: str = None,
    ):
        """
        Get invoices by document (either taker or issuer) with competence_date
        within the last three months.

        Args:
            taker_document: Document of the invoice taker (optional)
            issuer_document: Document of the invoice issuer (optional)
            competence: Competence period in YYYY-MM format
             (optional, defaults to last 3 months)

        Returns:
            List of invoices matching the criteria

        Raises:
            InvoiceNotFoundException: When no invoices are found
            ValueError: When neither taker_document nor issuer_document is provided
        """
        try:
            self._validate_documents(taker_document, issuer_document)

            start_date, end_date = self._calculate_date_range(competence)

            filters = self._build_filter_conditions(
                taker_document, issuer_document, start_date, end_date
            )

            invoices = Invoice.filter(*filters)

            if not invoices:
                error_message = self._format_not_found_message(taker_document, issuer_document)
                raise InvoiceNotFoundException(message=error_message)

            self._log_success(invoices, taker_document, issuer_document, start_date, end_date)

            return invoices

        except InvoiceNotFoundException:
            raise
        except ValueError as e:
            logger.error(f"Invalid parameters: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error retrieving invoices: {str(e)}")
            return None
