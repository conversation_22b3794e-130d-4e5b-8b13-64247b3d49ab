from datetime import date, datetime

from dateutil.relativedelta import relativedelta
from sqlalchemy import or_

from domain.exceptions import InvoiceNotFoundException
from domain.models import Invoice
from domain.repositories import AbstractInvoiceRepository
from shared.auth import Session
from shared.logger import get_logger

logger = get_logger()


class InvoiceRepository(AbstractInvoiceRepository):
    def __init__(self, session: Session) -> None:
        super().__init__()
        self.session = session

    def get_by_cnpj(
        self,
        taker_document: str = None,
        issuer_document: str = None,
        competence: str = None,
    ):
        """
        Get invoices by document (either taker or issuer) with competence_date
        within the last three months.

        Args:
            taker_document: Document of the invoice taker (optional)
            issuer_document: Document of the invoice issuer (optional)
            competence: Competence period in YYYY-MM format (optional, defaults to last 3 months)

        Returns:
            List of invoices matching the criteria

        Raises:
            InvoiceNotFoundException: When no invoices are found
            ValueError: When neither taker_document nor issuer_document is provided
        """
        try:
            if not taker_document and not issuer_document:
                raise ValueError(
                    "Either taker_document or issuer_document must be provided"
                )

            if competence:
                competence_date = datetime.strptime(competence, "%Y-%m").date()
                end_date = competence_date
            else:
                end_date = date.today()

            start_date = end_date - relativedelta(months=3)

            filters = []

            document_conditions = []
            if taker_document:
                document_conditions.append(Invoice.taker_document == taker_document)
            if issuer_document:
                document_conditions.append(Invoice.issuer_document == issuer_document)

            if document_conditions:
                filters.append(or_(*document_conditions))

            filters.append(Invoice.competence_date >= start_date)
            filters.append(Invoice.competence_date <= end_date)

            invoices = Invoice.filter(*filters)

            if not invoices:
                document_info = []
                if taker_document:
                    document_info.append(f"taker_document: {taker_document}")
                if issuer_document:
                    document_info.append(f"issuer_document: {issuer_document}")

                raise InvoiceNotFoundException(
                    message=f"for {', '.join(document_info)} within the last 3 months"
                )

            logger.info(
                f"Found {len(invoices)} invoices for documents "
                f"(taker: {taker_document}, issuer: {issuer_document}) "
                f"between {start_date} and {end_date}"
            )

            return invoices

        except InvoiceNotFoundException:
            raise
        except ValueError as e:
            logger.error(f"Invalid parameters: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error retrieving invoices: {str(e)}")
            return None
