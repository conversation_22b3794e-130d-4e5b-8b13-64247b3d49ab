from typing import Dict, List
from uuid import UUID

from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import selectinload

from domain.exceptions import ReconciliationNotFoundException
from domain.models import (
    BankAccountStatus,
    JournalHeader,
    Reconciliation,
    ReconciliationStatusEnum,
    SuggestedInvoice,
)
from domain.repositories import (
    AbstractMatchRepository,
    AbstractReconciliationRepository,
)
from shared.session_manager import session_scope


class ReconciliationRepository(AbstractReconciliationRepository):
    def __init__(self) -> None:
        super().__init__()

    def create(
        self,
        cockpit_customer_id: str,
        customer_cnpj: str,
        operator: str,
        status: ReconciliationStatusEnum,
        competence: str,
        bank_account_guids: List[str],
        status_bank_account_guids: List[str],
        journal_headers: List[JournalHeader],
    ):
        reconciliation = Reconciliation(
            cockpit_customer_id=cockpit_customer_id,
            customer_cnpj=customer_cnpj,
            operator=operator,
            status=status,
            competence=competence,
            bank_account_guids=bank_account_guids,
            journal_headers=journal_headers,
            status_bank_account_guids=status_bank_account_guids,
        )
        reconciliation.save()

        return reconciliation

    def set_status(
        self, reconciliation: Reconciliation, status: ReconciliationStatusEnum
    ):
        reconciliation.status = status
        reconciliation.save()

        return reconciliation

    def set_bank_status(
        self,
        reconciliation: Reconciliation,
        status_bank_account_guids: Dict[str, BankAccountStatus],
    ):
        reconciliation.status_bank_account_guids = status_bank_account_guids
        reconciliation.save(refresh=True)

        return reconciliation

    def get_reconciliation(self, id: int):
        try:
            reconciliation = Reconciliation.get(id)
            return reconciliation

        except NoResultFound:
            raise ReconciliationNotFoundException(message=f"Reconciliation ID {id}")
        except Exception:
            return None

    def get_reconciliations_by_customer_and_competence(
        self, cockpit_customer_id: str, competence: str
    ):
        return Reconciliation.filter(
            Reconciliation.cockpit_customer_id == cockpit_customer_id,
            Reconciliation.competence == competence,
        )

    def check_all_journal_headers_have_suggestions(
        self,
        reconciliation: Reconciliation,
        bankaccount_guid: str,
        match_repository: AbstractMatchRepository,
    ) -> bool:
        """
        Check if all journal headers for the given bank account
        have at least one suggested invoice.
        """
        journal_headers = [
            jh
            for jh in reconciliation.journal_headers
            if jh.cockpit_account_id == bankaccount_guid
        ]

        if not journal_headers:
            return True

        transaction_ids = [jh.source_reference_id for jh in journal_headers]
        suggestions_count = match_repository.count_by_transaction_ids(transaction_ids)
        suggestions_dict = dict(suggestions_count)

        return all(tid in suggestions_dict for tid in transaction_ids)

    def get_reconciliation_with_suggested_accounts(self, reconciliation_id: UUID):
        with session_scope(close=False) as session:
            try:
                reconciliation = (
                    session.query(Reconciliation)
                    .options(
                        selectinload(Reconciliation.journal_headers).selectinload(
                            JournalHeader.journal_lines
                        )
                    )
                    .filter(Reconciliation.id == reconciliation_id)
                    .one()
                )

                source_ids = [
                    h.source_reference_id for h in reconciliation.journal_headers
                ]

                suggested_accounts = (
                    session.query(SuggestedInvoice)
                    .filter(
                        SuggestedInvoice.platform_transaction_id.in_(source_ids),
                        SuggestedInvoice.suggestion_type == "ACCOUNT",
                    )
                    .all()
                )

                suggested_map = {
                    s.platform_transaction_id: s for s in suggested_accounts
                }
                for header in reconciliation.journal_headers:
                    suggested = suggested_map.get(header.source_reference_id)
                    for line in header.journal_lines:
                        line._suggested_account = suggested

            except NoResultFound:
                raise ReconciliationNotFoundException(
                    message=f"Reconciliation ID {reconciliation_id} not found."
                )
        return reconciliation
