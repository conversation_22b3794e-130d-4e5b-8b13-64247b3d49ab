from datetime import datetime
from typing import Dict, List, Union
from uuid import UUID

from pydantic import BaseModel, root_validator
from pydantic.schema import Optional

from domain.models import (
    Account,
    AccountStatus,
    AccountTypeEnum,
    JournalHeader,
    JournalLine,
    OperationTypeEnum,
    ReconciliationStatusEnum,
    TransactionStatusEnum,
    TransactionTypeEnum,
)
from infrastructure.repositories.reconciliation_repository import (
    ReconciliationRepository,
)
from shared.logger import get_logger

logger = get_logger()


class AccountStatementResponse(BaseModel):
    """Account Statement Mapper"""

    latest_uploads_by_account: Dict[str, Dict]
    status_bank_account_guids: Dict[str, str]

    @classmethod
    def build(cls, latest_uploads_by_account, status_bank_account_guids):
        return cls(
            latest_uploads_by_account=latest_uploads_by_account,
            status_bank_account_guids=status_bank_account_guids,
        )


class FileType(BaseModel):
    """Account Statement Mapper"""

    content_type: str

    @classmethod
    def build(cls, extension: str):
        content_types = {
            "pdf": "application/pdf",
            "ofx": "application/ofx",
            "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        }

        normalized_extension = extension.lower().lstrip(".")
        content_type = content_types.get(normalized_extension)
        if content_type is None:
            raise ValueError(f"Unsupported file extension: {extension}")

        return cls(content_type=content_type)


class LinkedInvoiceResponse(BaseModel):
    """Linked Invoice Mapper"""

    platform_invoice_id: str
    link_date: datetime
    link_percentual: Optional[float]
    invoice_number: Optional[str]
    file_path: Optional[str]

    class Config:
        orm_mode = True

    @classmethod
    def build(cls, linked_invoice):
        return cls.from_orm(linked_invoice)


class SuggestedInvoiceResponse(BaseModel):
    """Suggested Invoice Mapper"""

    platform_invoice_id: str
    platform_transaction_id: str
    ranking: int
    exact_match: bool
    invoice_date: str
    invoice_company_name: str
    invoice_value: str
    invoice_number: str

    class Config:
        orm_mode = True

    @classmethod
    def build(cls, suggestion):
        return cls.from_orm(suggestion)


class PartnerEntityResponse(BaseModel):
    """Partner Entity table"""

    cpf_cnpj: Optional[str]
    name: Optional[str]
    description: Optional[str]

    class Config:
        orm_mode = True

    @classmethod
    def build(cls, partner_entity):
        return cls.from_orm(partner_entity)


class AccountResponse(BaseModel):
    """Account table"""

    id: str
    cnpj: str
    parent: Optional[str] = None
    description: str
    classification: str
    level: int
    short_code: int
    status: AccountStatus
    account_type: AccountTypeEnum
    operation_type: OperationTypeEnum

    @classmethod
    def build(cls, account: Account):
        return cls(
            id=str(account.id),
            cnpj=account.cnpj,
            parent_code=account.parent_code,
            description=account.description,
            classification=account.classification,
            level=account.level,
            short_code=account.short_code,
            status=account.status,
            account_type=account.account_type,
            operation_type=account.operation_type,
        )


class JournalLineResponse(BaseModel):
    """Movement (sub-transaction) schema"""

    id: Union[str, UUID]
    parentId: str
    date: datetime
    description: str
    amount: float
    expense_account: Optional[str] = ""
    income_account: Optional[str] = ""
    status: TransactionStatusEnum
    notes: Optional[str] = None
    line_type: OperationTypeEnum
    platform_transaction_id: str = ""
    trading_entity: Optional[PartnerEntityResponse] = None
    suggested_invoices: List[SuggestedInvoiceResponse] = []
    suggested_account: Optional[Dict[Optional[str], Optional[str]]] = None
    linked_invoices: List[LinkedInvoiceResponse] = []
    is_linked_invoice_applicable: Optional[bool] = None
    has_linked_invoices: Optional[bool] = None
    transaction_metadata: Optional[Dict] = {}
    parent_status: TransactionStatusEnum = TransactionStatusEnum.NEW
    cockpit_account_id: str = ""

    class Config:
        orm_mode = True

    @classmethod
    def build(cls, journal_line: JournalLine):
        trading_entity = None
        if journal_line.journal_header.partner_entity:
            trading_entity = PartnerEntityResponse.build(
                journal_line.journal_header.partner_entity
            )

        linked_invoices_applicable = (
            journal_line._metadata.get("is_linked_invoice_applicable")
            if journal_line._metadata
            else None
        )

        suggested_invoices = []
        if journal_line.journal_header.suggested_invoices:
            suggested_invoices = [
                SuggestedInvoiceResponse.build(si)
                for si in journal_line.journal_header.suggested_invoices
            ]

        journal_line_response = cls(
            id=str(journal_line.id),
            parentId=str(journal_line.journal_header_id),
            date=journal_line.journal_header.date,
            description=journal_line.description,
            amount=float(journal_line.amount)
            * (-1 if journal_line.line_type == "DEBIT" else 1),
            expense_account=(
                (journal_line.account_id or "")
                if journal_line.line_type == "DEBIT"
                else ""
            ),
            income_account=(
                (journal_line.account_id or "")
                if journal_line.line_type == "CREDIT"
                else ""
            ),
            status=journal_line.determine_status(),
            notes=journal_line.notes,
            line_type=journal_line.line_type,
            platform_transaction_id=journal_line.journal_header.source_reference_id,
            trading_entity=trading_entity,
            suggested_invoices=suggested_invoices,
            suggested_account=journal_line.suggested_account,
            linked_invoices=[
                LinkedInvoiceResponse.build(li)
                for li in (journal_line.linked_invoices or [])
            ],
            is_linked_invoice_applicable=linked_invoices_applicable,
            has_linked_invoices=(
                journal_line._metadata.get("has_linked_invoices")
                if journal_line._metadata
                else None
            ),
            metadata=journal_line._metadata or {},
            parent_status=journal_line.journal_header.status,
        )

        return journal_line_response


class JournalHeaderResponse(BaseModel):
    """Main transaction schema with simplified fields and movements array"""

    id: Union[str, UUID]
    date: datetime
    description: str
    amount: float
    expense_account: str = ""
    income_account: str = ""
    cockpit_account_id: str = ""
    platform_transaction_id: str = ""
    supplier_id: str = ""
    status: TransactionStatusEnum
    updated_at: str = ""
    trading_entity: Optional[PartnerEntityResponse] = None
    supplier: Optional[PartnerEntityResponse] = None
    customer: Optional[PartnerEntityResponse] = None
    journal_lines: List[JournalLineResponse] = []
    transaction_metadata: Optional[Dict] = {}
    notes: Optional[str] = None

    @classmethod
    def build(cls, journal_header: JournalHeader):
        built_supplier = None
        built_customer = None
        partner_entity = (
            PartnerEntityResponse.build(journal_header.partner_entity)
            if journal_header.partner_entity_id
            else None
        )
        built_supplier = (
            partner_entity
            if journal_header.type == TransactionTypeEnum.EXPENSE
            else None
        )
        built_customer = (
            partner_entity
            if journal_header.type == TransactionTypeEnum.INCOME
            else None
        )

        trading_entity = None
        if built_supplier and (built_supplier.cpf_cnpj or built_supplier.name):
            trading_entity = built_supplier
        elif built_customer and (built_customer.cpf_cnpj or built_customer.name):
            trading_entity = built_customer

        journal_header_response = cls(
            id=str(journal_header.id),
            date=journal_header.date,
            description=journal_header.description,
            amount=float(journal_header.amount or 0),
            supplier=built_supplier,
            customer=built_customer,
            trading_entity=trading_entity,
            cockpit_account_id=journal_header.cockpit_account_id,
            platform_transaction_id=journal_header.source_reference_id,
            updated_at=str(journal_header.updated_at),
            status=journal_header.status,
            type=journal_header.type,
            transaction_metadata=journal_header._metadata or {},
            notes=journal_header.notes,
        )

        for journal_line in journal_header.journal_lines:
            journal_line_response = JournalLineResponse(
                id=str(journal_line.id),
                parentId=str(journal_header.id),
                date=journal_header.date,
                description=f"{journal_line.description}",
                amount=float(journal_line.amount)
                * (-1 if journal_line.line_type == "DEBIT" else 1),
                expense_account=(
                    journal_line.account_id if journal_line.line_type == "DEBIT" else ""
                ),
                income_account=(
                    journal_line.account_id
                    if journal_line.line_type == "CREDIT"
                    else ""
                ),
                status=journal_line.determine_status(),
                line_type=journal_line.line_type,
                notes=journal_line.notes,
                platform_transaction_id=journal_header.source_reference_id,
                trading_entity=trading_entity,
                suggested_account=journal_line.suggested_account,
                suggested_invoices=[
                    SuggestedInvoiceResponse.build(si)
                    for si in (journal_header.suggested_invoices or [])
                ],
                linked_invoices=[
                    LinkedInvoiceResponse.build(li)
                    for li in (journal_line.linked_invoices or [])
                ],
                is_linked_invoice_applicable=(
                    None
                    if journal_line._metadata is None
                    else journal_line._metadata.get("is_linked_invoice_applicable")
                ),
                has_linked_invoices=(
                    None
                    if journal_line._metadata is None
                    else journal_line._metadata.get("has_linked_invoices")
                ),
                transaction_metadata=journal_line._metadata or {},
                cockpit_account_id=journal_header.cockpit_account_id,
            )
            journal_header_response.journal_lines.append(journal_line_response)

        return journal_header_response


class TransactionResponse(BaseModel):
    """
    Unified Transaction schema, suitable for both 'read' and 'edit' contexts.

    If some fields only make sense in the edit context, you can keep them optional
    or set defaults (e.g., empty lists).
    """

    id: Union[str, UUID]
    amount: float
    date: datetime
    description: str
    notes: Optional[str] = None
    type: TransactionTypeEnum
    income_account: Optional[Union[str, AccountResponse]] = None
    expense_account: Optional[Union[str, AccountResponse]] = None
    status: TransactionStatusEnum
    cockpit_account_id: str

    trading_entity: Optional[PartnerEntityResponse] = None

    platform_transaction_id: str

    is_linked_invoice_applicable: Optional[bool] = None
    has_linked_invoices: Optional[bool] = None

    supplier: Optional[PartnerEntityResponse] = None
    customer: Optional[PartnerEntityResponse] = None
    linked_invoices: List[LinkedInvoiceResponse] = []
    suggested_invoices: List[SuggestedInvoiceResponse] = []
    suggested_account: Optional[Dict[str, str]] = None
    updated_at: Optional[str] = None
    transaction_metadata: Optional[Dict] = {}

    class Config:
        orm_mode = True

    @root_validator(pre=True, allow_reuse=True)
    def transformar_dados(cls, original_values):
        """Convert 'date' from string to datetime if needed."""
        values = dict(original_values)
        date_value = values.get("date")

        if isinstance(date_value, str):
            try:
                values["date"] = datetime.strptime(date_value, "%Y-%m-%dT%H:%M:%S")
            except ValueError:
                raise ValueError(f"Formato de data inválido: {date_value}")
        elif isinstance(date_value, datetime):
            values["date"] = date_value

        return values

    @classmethod
    def build(
        cls,
        transaction,
        supplier=None,
        customer=None,
        linked_invoices=None,
        suggested_invoices=None,
        suggested_account=None,
    ):
        """A single build method that can be used in both 'read' and 'edit' flows."""

        trading_entity = None
        if transaction.trading_identifier or transaction.trading_name:
            trading_entity = PartnerEntityResponse(
                cpf_cnpj=transaction.trading_identifier,
                name=transaction.trading_name,
                description=None,
            )
        if supplier is not None:
            built_supplier = PartnerEntityResponse.build(supplier)
        elif transaction.supplier:
            built_supplier = PartnerEntityResponse.build(transaction.supplier)
        else:
            built_supplier = None

        if customer is not None:
            built_customer = PartnerEntityResponse.build(customer)
        elif transaction.customer:
            built_customer = PartnerEntityResponse.build(transaction.customer)
        else:
            built_customer = None

        if linked_invoices is not None:
            built_linked_invoices = [
                LinkedInvoiceResponse.build(li) for li in linked_invoices
            ]
        else:
            built_linked_invoices = [
                LinkedInvoiceResponse.build(li)
                for li in getattr(transaction, "linked_invoices", []) or []
            ]

        if suggested_invoices is not None:
            built_suggested_invoices = [
                SuggestedInvoiceResponse.build(si) for si in suggested_invoices
            ]
        else:
            built_suggested_invoices = [
                SuggestedInvoiceResponse.build(si)
                for si in getattr(transaction, "suggested_invoices", []) or []
            ]

        return cls(
            id=str(transaction.id),
            amount=float(transaction.amount or 0),
            date=transaction.date,
            description=transaction.description,
            notes=transaction.notes,
            type=transaction.type,
            income_account=transaction.income_account,
            expense_account=transaction.expense_account,
            status=transaction.status,
            cockpit_account_id=transaction.cockpit_account_id,
            platform_transaction_id=transaction.platform_transaction_id,
            is_linked_invoice_applicable=getattr(
                transaction, "is_linked_invoice_applicable", None
            ),
            has_linked_invoices=getattr(transaction, "has_linked_invoices", None),
            trading_entity=trading_entity,
            supplier=built_supplier,
            customer=built_customer,
            linked_invoices=built_linked_invoices,
            suggested_invoices=built_suggested_invoices,
            suggested_account=transaction.suggested_account,
            updated_at=str(transaction.updated_at),
            transaction_metadata=transaction.transaction_metadata,
        )


class ReconciliationResponse(BaseModel):
    id: Union[str, UUID]
    competence: str
    status: ReconciliationStatusEnum
    cockpit_customer_id: str
    customer_cnpj: Optional[str] = None
    operator: str
    transactions: List[Union[TransactionResponse, JournalHeaderResponse]]
    bank_account_count: Dict[str, Dict]
    status_bank_account_guids: Optional[Dict[str, str]] = None
    latest_uploads_by_account: Optional[Dict[str, Dict]] = None

    class Config:
        orm_mode = True

    @classmethod
    def build(cls, reconciliation_id: UUID):
        repository = ReconciliationRepository()
        reconciliation = repository.get_reconciliation_with_suggested_accounts(
            reconciliation_id
        )

        logger.info(f"[Build transactions] FEATURE_FLAG_USE_NEW_JOURNAL_MODEL = {True}")
        transactions = list(
            JournalHeaderResponse.build(journal)
            for journal in reconciliation.journal_headers
        )

        return cls(
            id=reconciliation.id,
            competence=reconciliation.competence,
            status=reconciliation.status,
            cockpit_customer_id=reconciliation.cockpit_customer_id,
            customer_cnpj=reconciliation.customer_cnpj,
            operator=reconciliation.operator,
            transactions=transactions,
            bank_account_count=reconciliation.bank_account_count,
            status_bank_account_guids=reconciliation.status_bank_account_guids,
            latest_uploads_by_account=reconciliation.latest_uploads_by_account,
        )


class InvoicesResponse(BaseModel):
    """Invoices Mapper"""

    platform_invoice_id: str
    invoice_status: str
    invoice_number: str
    type: str
    competence_date: str
    issue_date: str
    amount: str
    netAmount: str
    issuerDocument: Optional[str]
    issuerName: Optional[str]
    takerDocument: Optional[str]
    takerName: Optional[str]
    providerName: Optional[str]
    customerDocument: Optional[str]
    key: Optional[str]
    verificationCode: Optional[str]
    subtype: Optional[str]
    serie: Optional[str]

    class Config:
        orm_mode = True

    @classmethod
    def build(cls, invoices):
        return cls.from_orm(invoices)


class ListInvoicesResponse(BaseModel):
    """List Invoices Mapper"""

    data: List[InvoicesResponse]
    offset: int
    nextOffset: int
