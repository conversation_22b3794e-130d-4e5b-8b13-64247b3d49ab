from typing import Dict, List

from fastapi import APIRouter, HTTPException, Request, Security, status
from pydantic import BaseModel

from api.mappers import ReconciliationResponse
from application.draft_reconciliation_service import DraftReconciliationService
from application.reconciliation_service import (
    download_excel_file,
    download_txt_file,
    set_reconciliation_bank_status,
    set_reconciliation_status,
)
from domain.exceptions import (
    BankAccountStatusNotFoundException,
    ReconciliationNotFinishedException,
    ReconciliationNotFoundException,
    ReconciliationStatusException,
)
from domain.models import BankAccountStatus, ReconciliationStatusEnum
from infrastructure.repositories.account_configuration_repository import (
    AccountConfigurationRepository,
)
from infrastructure.repositories.partner_entity_repository import (
    PartnerEntityRepository,
)
from infrastructure.repositories.platform_transaction_repository import (
    PlatformTransactionRepository,
)
from infrastructure.repositories.reconciliation_repository import (
    ReconciliationRepository,
)
from infrastructure.repositories.invoice import InvoiceRepository
from shared.auth import Claims, Scope, Session, auth
from shared.logger import get_logger, log_exception_error

router = APIRouter()

logger = get_logger()


class ReconciliationDownloadExcelRequest(BaseModel):
    id: str


class ReconciliationStatusRequest(BaseModel):
    id: str
    status: ReconciliationStatusEnum


class ReconciliationBankStatusRequest(BaseModel):
    id: str
    bank_account_status: Dict[str, BankAccountStatus]


@router.post("/status", response_model=ReconciliationResponse)
async def post_set_reconciliation_status(
    body: ReconciliationStatusRequest,
    _: Claims = Security(
        auth.verify,
        scopes=[Scope.UPDATE_RECONCILIATION_STATUS, Scope.MANAGE_RECONCILIATIONS],
    ),
):
    """
    Set Reconciliation Status endpoint
    """
    logger.info("[SetReconciliationStatus] Incoming request")
    logger.info(
        "[SetReconciliationStatus] reconciliation_id %s status %s", body.id, body.status
    )

    try:
        reconciliation = set_reconciliation_status(
            body.id, ReconciliationStatusEnum(body.status)
        )
        return ReconciliationResponse.build(reconciliation.id)
    except ReconciliationNotFoundException as not_found:
        raise HTTPException(status.HTTP_404_NOT_FOUND, not_found.__str__())
    except ReconciliationStatusException as status_exception:
        raise HTTPException(status.HTTP_409_CONFLICT, status_exception.__str__())
    except Exception as e:
        logger.info("[SetReconciliationStatus] Exception error")
        log_exception_error(e)

        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))


@router.post("/bank-account-status")
async def post_set_reconciliation_bank_account_status(
    body: ReconciliationBankStatusRequest,
    _: Claims = Security(
        auth.verify,
        scopes=[Scope.UPDATE_RECONCILIATION_STATUS, Scope.MANAGE_RECONCILIATIONS],
    ),
):
    """
    Set Reconciliation Status endpoint
    """
    logger.info("[SetReconciliationBankStatus] Incoming request")
    logger.info(
        "[SetReconciliationBankStatus] reconciliation_id %s status %s",
        body.id,
        body.bank_account_status,
    )

    try:
        return set_reconciliation_bank_status(body.id, body.bank_account_status)
    except ReconciliationNotFoundException as not_found:
        raise HTTPException(status.HTTP_404_NOT_FOUND, not_found.__str__())
    except BankAccountStatusNotFoundException as status_exception:
        raise HTTPException(status.HTTP_409_CONFLICT, status_exception.__str__())
    except Exception as e:
        logger.info("[SetReconciliationStatus] Exception error")
        log_exception_error(e)

        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))


@router.get("/download-files/{reconciliation_id}")
async def get_download_files(
    reconciliation_id: str,
    _: Request,
    claims: Claims = Security(
        auth.verify,
        scopes=[Scope.READ_RECONCILIATION_FILE_DOWNLOADS, Scope.MANAGE_RECONCILIATIONS],
    ),
):
    """
    Download excel and txt files
    """
    logger.info("[DownloadFiles] Incoming request")
    logger.info("[DownloadFiles] reconciliation_id %s", reconciliation_id)

    session = Session(
        user_id=claims.sub,
        email=claims.owner.email,
        name=claims.owner.name,
        token=claims.owner.token,
    )

    try:
        excel_content = download_excel_file(
            reconciliation_id=reconciliation_id, session=session
        )
        txt_content = download_txt_file(reconciliation_id=reconciliation_id)

        return {"excel": excel_content, "txt": txt_content}
    except ReconciliationNotFoundException as not_found:
        raise HTTPException(status.HTTP_404_NOT_FOUND, not_found.__str__())
    except ReconciliationNotFinishedException as not_finished:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, not_finished.__str__())
    except Exception as e:
        logger.info("[DownloadFiles] Exception error")
        log_exception_error(e)

        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))


@router.get("/{id}", response_model=ReconciliationResponse)
async def get_reconciliation_details(
    id: str,
    _: Claims = Security(
        auth.verify,
        scopes=[Scope.READ_RECONCILIATION_DETAILS, Scope.MANAGE_RECONCILIATIONS],
    ),
):
    """
    Get Reconciliation details
    """
    logger.info("[GetReconciliationDetails] Incoming request")
    logger.info("[GetReconciliationDetails] reconciliation_id %s", id)

    try:
        return ReconciliationResponse.build(reconciliation_id=id)
    except ReconciliationNotFoundException as not_found:
        raise HTTPException(status.HTTP_404_NOT_FOUND, not_found.__str__())
    except Exception as e:
        logger.info("[GetReconciliationDetails] Exception error")
        log_exception_error(e)
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))


class ReconciliationDraftRequest(BaseModel):
    cockpit_customer_id: str
    competence: str
    bank_account_guids: List[str]


@router.post("/draft", response_model=ReconciliationResponse)
async def get_reconciliation_draft(
    body: ReconciliationDraftRequest,
    _: Request,
    claims: Claims = Security(
        auth.verify,
        scopes=[Scope.READ_RECONCILIATION_DRAFT, Scope.MANAGE_RECONCILIATIONS],
    ),
):
    """
    Get Reconciliation details (draft)
    """
    logger.info("[GetReconciliationDraft] Incoming request")
    logger.info(
        "[GetReconciliationDraft] customerid %s, competence %s, bank_account %s",
        body.cockpit_customer_id,
        body.competence,
        body.bank_account_guids,
    )

    session = Session(
        user_id=claims.sub,
        email=claims.owner.email,
        name=claims.owner.name,
        token=claims.owner.token,
    )

    try:
        service = DraftReconciliationService(
            ReconciliationRepository(),
            PlatformTransactionRepository(
                session,
                PartnerEntityRepository(),
                AccountConfigurationRepository(),
            ),
            InvoiceRepository(session),
            session,
        )

        reconciliation = await service.handle_draft(
            body.cockpit_customer_id, body.competence, body.bank_account_guids
        )
        response = ReconciliationResponse.build(reconciliation.id)
        return response
    except ReconciliationNotFoundException as not_found:
        raise HTTPException(status.HTTP_404_NOT_FOUND, not_found.__str__())
    except Exception as e:
        logger.info(f"[GetReconciliationDraft] Exception error {str(e)}")
        log_exception_error(e)

        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))
