from contextlib import contextmanager
from contextvars import ContextVar

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from config.config import settings

engine = create_engine(
    settings.DATABASE_URI,
    pool_recycle=3600,
    isolation_level="READ COMMITTED",
    future=True,  # noqa
    pool_pre_ping=True,
)
_SessionMaker = sessionmaker(bind=engine, future=True, expire_on_commit=False)
session = _SessionMaker()
session_context = ContextVar("session_context", default=None)
session_context.set(session)
user_context = ContextVar("user_context", default=None)


@contextmanager
def session_scope(close=True):
    session = session_context.get()

    if session is None:
        session = _SessionMaker()
        session_context.set(session)

    try:
        yield session
    except Exception as e:
        session.rollback()
        raise e
    finally:
        if session is session_context.get() and close:
            session.close()  # Fecha a sessão se ela foi criada no escopo
