from typing import Dict, List, Union

from domain.models import SuggestedInvoice, SuggestionSource, SuggestionTypeEnum
from shared.utils.json import safe_get


def convert_to_suggested_invoice(
    match: Dict[str, str], source: Union[SuggestionSource, None] = None
) -> List[SuggestedInvoice]:
    suggestions = []

    if safe_get(match, ["platform_invoice_id"]):
        invoice_suggestion = SuggestedInvoice(
            suggestion_id=match["suggestion_id"],
            cockpit_account_id=match["cockpit_account_id"],
            platform_transaction_id=match["platform_transaction_id"],
            platform_invoice_id=match["platform_invoice_id"],
            ranking=match["ranking"] or 1,
            exact_match=match["exact_match"] or 0,
            version=match["version"],
            should_not_be_matched=match["should_not_be_matched"],
            account_data={"number": None, "rule_name": None, "rule_type": None},
            suggestion_type=SuggestionTypeEnum.INVOICE,
            source=source,
        )
        suggestions.append(invoice_suggestion)

    if safe_get(match, ["suggested_account"]):
        account_suggestion = SuggestedInvoice(
            suggestion_id=match["suggestion_id"],
            cockpit_account_id=match["cockpit_account_id"],
            platform_transaction_id=match["platform_transaction_id"],
            platform_invoice_id=None,
            ranking=match["ranking"] or 1,
            exact_match=match["exact_match"] or 0,
            version=match["version"],
            should_not_be_matched=match["should_not_be_matched"],
            account_data={
                "number": safe_get(match, ["suggested_account"]),
                "rule_type": safe_get(match, ["categorization_rule", "kind"]),
                "rule_name": safe_get(match, ["categorization_rule", "name"]),
            },
            suggestion_type=SuggestionTypeEnum.ACCOUNT,
            source=source,
        )
        suggestions.append(account_suggestion)

    if not suggestions:
        info_suggestion = SuggestedInvoice(
            suggestion_id=match["suggestion_id"],
            cockpit_account_id=match["cockpit_account_id"],
            platform_transaction_id=match["platform_transaction_id"],
            platform_invoice_id=None,
            ranking=match["ranking"] or 1,
            exact_match=match["exact_match"] or 0,
            version=match["version"],
            should_not_be_matched=match["should_not_be_matched"],
            account_data={"number": None, "rule_name": None, "rule_type": None},
            suggestion_type=SuggestionTypeEnum.INFO,
            source=source,
        )
        suggestions.append(info_suggestion)

    return suggestions
