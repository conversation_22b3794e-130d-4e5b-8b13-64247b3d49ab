import json
from os import environ
from typing import Any, Dict, Optional

import boto3
from botocore.config import Config as BotoConfig
from dotenv import load_dotenv
from pydantic import BaseSettings, SecretStr, parse_obj_as

TEST = "test"
PRODUCTION = "prod"
DEVELOPMENT = "development"
STAGING = "staging"


class ImproperlyConfigured(Exception):
    pass


class Config(BaseSettings):
    """Base configuration."""

    TESTING: bool = False
    DOCUMENTS_STORAGE_DRIVER: str = "dummy"
    DATABASE_SECRET_ARN: Optional[SecretStr] = None
    DEBUG: bool = True

    AUTH0_CLAIMS_NAMESPACE: str = "https://cockpit.cexp-dev.bhub.ai"
    AUTH0_DOMAIN: str = "auth.cockpit.cexp-dev.bhub.ai"
    AUTH0_ALGORITHMS: str = "RS256"
    AUTH0_AUDIENCE: str = "https://conciliador-api.caasdev.bhub.ai"
    AUTH0_ISSUER: str = "https://auth.cockpit.cexp-dev.bhub.ai/"

    CONCILIATOR_CLIENT_ID: str = "bdjGtp2IbCr34j0PhDIobObnR73k3e5H"
    CONCILIATOR_CLIENT_SECRET: str = (
        "wKlZJncd6oT2SCML3vrgyFlN3GUnV9FMvvIm_nNCyhil5LwtFXvhVCcwGT3gZvmB"
    )

    COCKPIT_AUTH0_AUDIENCE: str = "https://cockpit.cexp-dev.bhub.ai/api/"
    COCKPIT_AUTH_TOKEN: str = "6b9ca962989b5d49c06cf6982cd272afd6d50d38"

    INTEGRADOR_NF_AUDIENCE: str = "https://integradornf.caasdev.bhub.ai"

    @property
    def DATABASE_URI(self) -> str:
        """Returns PostgreSQL's database URI based on environment variables.

        Returns
        -------
        str
            The database URI to be used with PostgreSQL.
        """
        secret_value = self._secret_value_from_arn(
            self.DATABASE_SECRET_ARN.get_secret_value()
        )

        user = secret_value["username"]
        password = secret_value["password"]
        database_name = secret_value["dbname"]
        database_host = secret_value["host"]
        port = secret_value.get("port", "5432")

        return f"postgresql://{user}:{password}@{database_host}:{port}/{database_name}"

    @property
    def ENCRYPT_KEY(self) -> str:
        return environ.get("ENCRYPT_KEY")

    @property
    def CAMUNDA_ENDPOINT(self) -> str:
        return environ.get("CAMUNDA_ENDPOINT")

    @property
    def CAMUNDA_USERNAME(self) -> str:
        return environ.get("CAMUNDA_USERNAME")

    @property
    def CAMUNDA_PASSWORD(self) -> str:
        return environ.get("CAMUNDA_PASSWORD")

    @property
    def CAMUNDA_PROCESS_KEY(self) -> str:
        return environ.get("CAMUNDA_PROCESS_KEY")

    @property
    def CAMUNDA_QUEUE_URL(self) -> str:
        return environ.get("CAMUNDA_QUEUE_URL")

    @property
    def INVOICES_API_V2_BASEURL(self) -> str:
        return "https://integradornf.caasdev.bhub.ai"

    @property
    def COCKPIT_AUTH_URL(self) -> str:
        return "https://auth.cockpit.cexp-dev.bhub.ai/oauth/token"

    @property
    def INTEGRADOR_NF_CLIENT_ID(self) -> str:
        return "bdjGtp2IbCr34j0PhDIobObnR73k3e5H"

    @property
    def INTEGRADOR_NF_CLIENT_SECRET(self) -> str:
        return "wKlZJncd6oT2SCML3vrgyFlN3GUnV9FMvvIm_nNCyhil5LwtFXvhVCcwGT3gZvmB"

    @property
    def TRANSACTIONS_API_BASEURL(self) -> str:
        return environ.get(
            "TRANSACTIONS_API_BASEURL", "https://statements-api.caasdev.bhub.ai"
        )

    @property
    def MATCH_BUCKET(self) -> str:
        return environ.get("MATCH_BUCKET")

    @property
    def ACCOUNT_BUCKET(self) -> str:
        return environ.get("ACCOUNT_BUCKET")

    @property
    def FEATURE_FLAG_SIMPLIFIED_CATEGORIZER(self) -> bool:
        return parse_obj_as(
            bool, environ.get("FEATURE_FLAG_SIMPLIFIED_CATEGORIZER", "True")
        )

    @property
    def COCKPIT_API_BASEURL(self) -> str:
        return environ.get("COCKPIT_API_BASEURL", "https://cockpit.cexp-dev.bhub.ai")

    @property
    def UPLOAD_API_BASEURL(self) -> str:
        return environ.get(
            "UPLOAD_API_BASE_URL",
            "https://statements-api.caasdev.bhub.ai",
        )

    def _secret_value_from_arn(self, secret_arn) -> Dict[str, Any]:
        """Returns the secret value from the given secret ARN.

        Parameters
        ----------
        secret_arn_variable : str
            The name of the environment variable containing the secret ARN.

        Returns
        -------
        Dict[str, Any]
            The secret value.
        """
        client = boto3.client(
            "secretsmanager",
            config=BotoConfig(
                connect_timeout=3,
                read_timeout=3,
            ),
        )

        secret = client.get_secret_value(SecretId=secret_arn)
        secret_value = json.loads(secret["SecretString"])

        return secret_value


class Development(Config):
    """Development configuration."""

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    @property
    def DATABASE_URI(self) -> str:
        """Returns PostgreSQL's database URI based on environment variables.

        Returns
        -------
        str
            The database URI to be used with PostgreSQL.

        Raises
        ------
        ImproperlyConfigured
            Raised when any of the following environment variables is missing
            `POSTGRESQL_USER`, `POSTGRESQL_PASSWORD`, `POSTGRESQL_DB`, `POSTGRESQL_HOST`
        """

        try:
            user = environ["POSTGRESQL_USER"]
            password = environ["POSTGRESQL_PASSWORD"]
            database_name = environ["POSTGRESQL_DB"]
            database_host = environ["POSTGRESQL_HOST"]
            port = environ.get("POSTGRESQL_PORT", "5432")

            return (
                f"postgresql://{user}:{password}@{database_host}:{port}/{database_name}"
            )
        except KeyError as error:
            raise ImproperlyConfigured(
                f"Missing '{error.args[0]}' environment variable"
            )


class Test(Development):
    """Test configuration."""

    DEBUG: bool = False
    TESTING: bool = True

    class Config:
        env_file = ".env.test"
        env_file_encoding = "utf-8"

    @property
    def DATABASE_URI(self) -> str:
        """Returns PostgreSQL's database URI based on environment variables.

        Returns
        -------
        str
            The database URI to be used with PostgreSQL.

        Raises
        ------
        ImproperlyConfigured
            Raised when any of the following environment variables is missing
            `POSTGRESQL_USER`, `POSTGRESQL_PASSWORD`, `POSTGRESQL_DB`, `POSTGRESQL_HOST`
        """

        try:
            user = environ.get("POSTGRESQL_USER", "admin")
            password = environ.get("POSTGRESQL_PASSWORD", "admin")
            database_name = environ.get("POSTGRESQL_DB", "conciliador_back_test")
            database_host = environ.get("POSTGRESQL_HOST", "localhost")
            port = environ.get("POSTGRESQL_PORT", "5432")

            return (
                f"postgresql://{user}:{password}@{database_host}:{port}/{database_name}"
            )
        except KeyError as error:
            raise ImproperlyConfigured(
                f"Missing '{error.args[0]}' environment variable"
            )

    @property
    def INVOICES_API_V2_BASEURL(self) -> str:
        return "https://test.local"

    @property
    def COCKPIT_AUTH_URL(self) -> str:
        return "https://test.local/oauth/token"

    @property
    def INTEGRADOR_NF_CLIENT_ID(self) -> str:
        return "1234"

    @property
    def INTEGRADOR_NF_CLIENT_SECRET(self) -> str:
        return "1234"

    @property
    def TRANSACTIONS_API_BASEURL(self) -> str:
        return "http://test.transactions"

    @property
    def CAMUNDA_PROCESS_KEY(self) -> str:
        return "camunda_process_key_test"

    @property
    def CAMUNDA_QUEUE_URL(self) -> str:
        return environ.get("TEST_CAMUNDA_QUEUE_URL")


class Production(Config):
    """Production configuration."""

    DEBUG: bool = False
    TESTING: bool = False
    DOCUMENTS_STORAGE_DRIVER: str = "aws:s3"

    AUTH0_CLAIMS_NAMESPACE: str = "https://cockpit.bhub.ai"
    AUTH0_DOMAIN: str = "auth.cockpit.bhub.ai"
    AUTH0_ALGORITHMS: str = "RS256"
    AUTH0_AUDIENCE: str = "https://conciliador-api.bhub.ai"
    AUTH0_ISSUER: str = "https://auth.cockpit.bhub.ai/"

    COCKPIT_AUTH_TOKEN: str = "d3fbad908b61f75d742935bf42255ea1a0268cb3"

    INTEGRADOR_NF_AUDIENCE: str = "https://integradornf.bhub.ai"

    @property
    def INVOICES_API_V1_BASEURL(self) -> str:
        return "https://cvip7oi0wa.execute-api.us-east-1.amazonaws.com/prod"

    @property
    def INVOICES_API_V1_APIKEY(self) -> str:
        return "BD9LS1qxJ12ydLkx5BlGU2I5uZCXkRjG2PDGPjTe"

    @property
    def TRANSACTIONS_API_BASEURL(self) -> str:
        return "https://statements-api.bhub.ai"

    @property
    def INTEGRADOR_NF_CLIENT_ID(self) -> str:
        return "D0Z0ub68dk81xWWQKTi3VyHRehKVWkGR"

    @property
    def INTEGRADOR_NF_CLIENT_SECRET(self) -> str:
        return "tStEZbhhPHyYK38hhozSQHIk72bNzV8mCKLm0HGPZId435bRWliCR6nmF4TMXvnt"

    @property
    def INVOICES_API_V2_BASEURL(self) -> str:
        return "https://integradornf.bhub.ai"


class Staging(Config):
    """Development configuration."""

    DEBUG: bool = True
    TESTING: bool = False


def _load_settings(env: str) -> Config:
    """Loads the settings based on the given environment.

    Parameters
    ----------
    env : str
        The environment to load the settings from.

    Returns
    -------
    Config
        Application settings.
    """

    if env == PRODUCTION:
        return Production()

    if env == STAGING:
        return Staging()

    if env == TEST:
        return Test()

    load_dotenv()
    return Development()


env = environ.get("APP_ENV", DEVELOPMENT)

settings = _load_settings(env)
